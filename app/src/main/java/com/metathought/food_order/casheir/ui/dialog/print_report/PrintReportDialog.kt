package com.metathought.food_order.casheir.ui.dialog.print_report


import android.Manifest
import android.app.DatePickerDialog
import android.content.Context
import android.content.DialogInterface
import android.content.pm.PackageManager
import android.icu.util.Calendar
import android.media.MediaScannerConnection
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.DatePicker
import android.widget.EditText
import android.widget.PopupWindow
import android.widget.TextView
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.FORMAT_DATE_TIME_SHOW
import com.metathought.food_order.casheir.constant.ReportExportEnum
import com.metathought.food_order.casheir.constant.ReportFileType
import com.metathought.food_order.casheir.databinding.DialogPrintReportBinding
import com.metathought.food_order.casheir.databinding.PopupPrintReportTypeBinding
import com.metathought.food_order.casheir.extension.formatDateStr
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.parseDate
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.helper.FolderHelper
import com.metathought.food_order.casheir.helper.PopupWindowHelper
import com.metathought.food_order.casheir.network.download.DownloadListener
import com.metathought.food_order.casheir.network.download.DownloadManager
import com.metathought.food_order.casheir.ui.common.RangeTimePickerDialog
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.utils.FileUtil
import com.metathought.food_order.casheir.utils.SingleClickUtils
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import java.io.File
import java.util.Date


/**
 * 打印报表 (2025、04、15 废弃)
 * **/

@AndroidEntryPoint
class PrintReportDialog : BaseDialogFragment() {
    private var binding: DialogPrintReportBinding? = null
    private val viewModel: PrintReportViewModel by viewModels()

    private val PERMISSION_REQUEST_CODE = 1

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogPrintReportBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initListener()
        initObserver()
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.85).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.4).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    private fun initData() {
        binding?.apply {
            selectType = arguments?.getInt(REPORT_TYPE)
        }
    }

    private fun initObserver() {
        viewModel.uiState.observe(viewLifecycleOwner) { state ->
            if (state.isRequestSuccess == true) {
                dismissAllowingStateLoss()
            }

            binding?.apply {
                btnYes.setEnableWithAlpha(state.isEnableRequest == true)
            }
        }
    }

    private var isQuickSelect = false

    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismissAllowingStateLoss()
            }

            tvToday.setOnClickListener {
                isQuickSelect = true
                resetSelect()
                tvToday.isSelected = true
                val endCalendar = Calendar.getInstance()
                tvEndTime.text = endCalendar.time.formatDateStr(FORMAT_DATE_TIME_SHOW)
                endCalendar.set(Calendar.HOUR_OF_DAY, 0)
                endCalendar.set(Calendar.MINUTE, 0)
                endCalendar.set(Calendar.SECOND, 0)
                endCalendar.set(Calendar.MILLISECOND, 0)
                tvStartTime.text = endCalendar.time.formatDateStr(FORMAT_DATE_TIME_SHOW)
                checkBtnEnable()

            }

            tvYesterday.setOnClickListener {
                isQuickSelect = true
                resetSelect()
                tvYesterday.isSelected = true
                val calendar = Calendar.getInstance()
                calendar.add(Calendar.DAY_OF_MONTH, -1) // 先将当前日期调整为昨天

                // 获取昨天开始时间（零点整）
                calendar.set(Calendar.HOUR_OF_DAY, 0)
                calendar.set(Calendar.MINUTE, 0)
                calendar.set(Calendar.SECOND, 0)
                calendar.set(Calendar.MILLISECOND, 0)
                val startOfYesterday = calendar.time

                // 获取昨天结束时间（23点59分59秒）
                calendar.set(Calendar.HOUR_OF_DAY, 23)
                calendar.set(Calendar.MINUTE, 59)
                calendar.set(Calendar.SECOND, 59)
                calendar.set(Calendar.MILLISECOND, 999)
                val endOfYesterday = calendar.time

                tvStartTime.text = startOfYesterday.formatDateStr(FORMAT_DATE_TIME_SHOW)
                tvEndTime.text = endOfYesterday.formatDateStr(FORMAT_DATE_TIME_SHOW)
                checkBtnEnable()
            }

            tvLastWeek.setOnClickListener {
                isQuickSelect = true
                resetSelect()
                tvLastWeek.isSelected = true
                // 将日期回退到上周日
                val calendar = Calendar.getInstance()
                calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY)
                calendar.set(Calendar.HOUR_OF_DAY, 23)
                calendar.set(Calendar.MINUTE, 59)
                calendar.set(Calendar.SECOND, 59)
                calendar.set(Calendar.MILLISECOND, 999)
                val endOfLastWeek = (calendar.clone() as Calendar).time
                // 将日期回退到上周一
                calendar.add(Calendar.DAY_OF_MONTH, -6)
                calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
                calendar.set(Calendar.HOUR_OF_DAY, 0)
                calendar.set(Calendar.MINUTE, 0)
                calendar.set(Calendar.SECOND, 0)
                calendar.set(Calendar.MILLISECOND, 0)
                val startOfLastWeek = calendar.time
                tvStartTime.text = startOfLastWeek.formatDateStr(FORMAT_DATE_TIME_SHOW)
                tvEndTime.text = endOfLastWeek.formatDateStr(FORMAT_DATE_TIME_SHOW)
                checkBtnEnable()
            }

            tvLastMonth.setOnClickListener {
                isQuickSelect = true
                resetSelect()
                tvLastMonth.isSelected = true
                val calendar = Calendar.getInstance()
                // 将日期设置为上个月的第一天
                calendar.add(Calendar.MONTH, -1)
                calendar.set(Calendar.DAY_OF_MONTH, 1)
                calendar.set(Calendar.HOUR_OF_DAY, 0)
                calendar.set(Calendar.MINUTE, 0)
                calendar.set(Calendar.SECOND, 0)
                calendar.set(Calendar.MILLISECOND, 0)
                val startOfLastMonth = calendar.time

                // 将日期设置为上个月的最后一天
                calendar.set(
                    Calendar.DAY_OF_MONTH,
                    calendar.getActualMaximum(Calendar.DAY_OF_MONTH)
                )
                calendar.set(Calendar.HOUR_OF_DAY, 23)
                calendar.set(Calendar.MINUTE, 59)
                calendar.set(Calendar.SECOND, 59)
                calendar.set(Calendar.MILLISECOND, 999)
                val endOfLastMonth = calendar.time
                tvStartTime.text = startOfLastMonth.formatDateStr(FORMAT_DATE_TIME_SHOW)
                tvEndTime.text = endOfLastMonth.formatDateStr(FORMAT_DATE_TIME_SHOW)
                checkBtnEnable()
            }

            edtType.setOnClickListener {
                showPopupWindow(textInputLayoutType)
            }

            edtStartTime.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    showDateTimePicker(edtStartTime, tvStartTime)
                }

            }

            edtEndTime.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    showDateTimePicker(edtEndTime, tvEndTime)
                }
            }

            radioPrintType.setOnCheckedChangeListener { radioGroup, i ->
                showFormatSelect()
            }

            btnYes.setOnClickListener {
                if (selectType == null) {
                    return@setOnClickListener
                }
                binding?.apply {
                    if (radioPrintType.checkedRadioButtonId == R.id.radioExport) {
                        //如果是导出
                        if (checkPermissions()) {
                            viewModel.export(
                                requireActivity(),
                                tvStartTime.text.toString(),
                                tvEndTime.text.toString(),
                                reportType = selectType!!,
                                fileType = if (exportFormatsRadioType.checkedRadioButtonId == R.id.radioExcel) ReportFileType.EXCEL.id else ReportFileType.PDF.id
                            ) {
                                if (it != null)
                                    download(it)
                            }
                        } else {
                            requestPermissions()
                        }
                    } else {
                        if (selectType == ReportExportEnum.PRODUCT_REPORT.id) {
                            viewModel.printProductReport(
                                requireActivity(),
                                tvStartTime.text.toString(),
                                tvEndTime.text.toString(), "", listOf(),""
                            )
                        } else if (selectType == ReportExportEnum.OFFLINE_CHANNEL_REPORT.id) {
                            viewModel.printPaymentReport(
                                requireActivity(),
                                tvStartTime.text.toString(),
                                tvEndTime.text.toString()
                            )
                        }
                    }
                }

            }
        }
    }

    private fun showFormatSelect() {
        binding?.apply {
            if (radioPrintType.checkedRadioButtonId == R.id.radioExport) {
                if (selectType == null) {
                    llExportFormatGroupType.isVisible = false
                } else {
                    llExportFormatGroupType.isVisible = true
                    if (selectType == ReportExportEnum.OFFLINE_CHANNEL_REPORT.id) {
                        //如果是支付报表
                        radioExcel.isVisible = false
                        exportFormatsRadioType.check(R.id.radioPdf)
                    } else {
                        radioExcel.isVisible = true
                    }
                }
                btnYes.setText(R.string.export)
            } else {
                llExportFormatGroupType.isVisible = false
                btnYes.setText(R.string.printer)
            }
        }
    }


    private fun checkPermissions(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            Environment.isExternalStorageManager()
        } else {
            val readPermission = ContextCompat.checkSelfPermission(
                requireActivity(),
                Manifest.permission.READ_EXTERNAL_STORAGE
            )
            val writePermission = ContextCompat.checkSelfPermission(
                requireActivity(),
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            )
            readPermission == PackageManager.PERMISSION_GRANTED &&
                    writePermission == PackageManager.PERMISSION_GRANTED
        }
    }

    private fun requestPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            try {
                val intent = android.content.Intent(
                    android.provider.Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION
                )
                startActivity(intent)
            } catch (e: Exception) {
                val intent = android.content.Intent()
                intent.action =
                    android.provider.Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION
                intent.addCategory("android.intent.category.DEFAULT")
                intent.data = android.net.Uri.parse(
                    String.format(
                        "package:%s",
                        requireActivity().packageName
                    )
                )
                startActivity(intent)
            }
        } else {
            requestPermissions(
                arrayOf(
                    Manifest.permission.READ_EXTERNAL_STORAGE,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                ),
                PERMISSION_REQUEST_CODE
            )
//            ActivityCompat.requestPermissions(
//                requireActivity(),
//                arrayOf(
//                    Manifest.permission.READ_EXTERNAL_STORAGE,
//                    Manifest.permission.WRITE_EXTERNAL_STORAGE
//                ),
//                PERMISSION_REQUEST_CODE
//            )
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PERMISSION_REQUEST_CODE) {
            if (grantResults.isNotEmpty() && grantResults.all { it == PackageManager.PERMISSION_GRANTED }) {
                if (selectType != null) {
                    binding?.apply {
                        viewModel.export(
                            requireActivity(),
                            tvStartTime.text.toString(),
                            tvEndTime.text.toString(),
                            reportType = selectType!!,
                            fileType = if (exportFormatsRadioType.checkedRadioButtonId == R.id.radioExcel) ReportFileType.EXCEL.id else ReportFileType.PDF.id
                        ) {
                            if (it != null)
                                download(it)
                        }
                    }
                }
            } else {
                Timber.e("权限被拒绝，无法下载文件")
//                Toast.e(this, "权限被拒绝，无法下载文件", Toast.LENGTH_SHORT).show()
            }
        }
    }


    private fun resetSelect() {
        binding?.apply {
            tvToday.isSelected = false
            tvYesterday.isSelected = false
            tvLastWeek.isSelected = false
            tvLastMonth.isSelected = false
            edtStartTime.setText("")
            edtEndTime.setText("")

        }
    }

    private fun checkBtnEnable() {
        binding?.apply {
            if (edtType.text.isNullOrEmpty() || tvStartTime.text.isNullOrEmpty() || tvEndTime.text.isNullOrEmpty()) {
                btnYes.setEnableWithAlpha(false)
            } else {

                val startTime =
                    tvStartTime.text.toString().parseDate(FORMAT_DATE_TIME_SHOW) ?: Date()
                val endTime = tvEndTime.text.toString().parseDate(FORMAT_DATE_TIME_SHOW) ?: Date()

                if (startTime.time > endTime.time) {
                    //如果选择的开始时间大于结束时间禁用了
                    btnYes.setEnableWithAlpha(false)
                } else {
                    btnYes.setEnableWithAlpha(true)
                }
            }
        }
    }

    private var selectType: Int? = null

    private fun showPopupWindow(anchorView: View) {
        activity?.hideKeyboard()

        val popupView = PopupPrintReportTypeBinding.inflate(layoutInflater)
        val popupWindow = PopupWindow(
            popupView.root,
            anchorView.measuredWidth,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )

        PopupWindowHelper.addPopupWindow(popupWindow)

        popupWindow.animationStyle = R.style.PopupAnimation
        popupWindow.showAsDropDown(anchorView)
        popupWindow.setOnDismissListener {

        }

        popupView.tvProduct.setOnClickListener {
            selectType = ReportExportEnum.PRODUCT_REPORT.id
            updateSelectType()
            popupWindow.dismiss()
        }

        popupView.tvPayment.setOnClickListener {
            selectType = ReportExportEnum.OFFLINE_CHANNEL_REPORT.id
            updateSelectType()
            popupWindow.dismiss()
        }

        popupView.tvSaleReport.setOnClickListener {
            selectType = ReportExportEnum.SALES_REPORT.id
            updateSelectType()
            popupWindow.dismiss()
        }
    }

    private fun updateSelectType() {
        binding?.apply {
            when (selectType) {
                ReportExportEnum.PRODUCT_REPORT.id -> {
                    radioPrint.isVisible = true
                    edtType.setText(getString(R.string.product_report))
                }

                ReportExportEnum.OFFLINE_CHANNEL_REPORT.id -> {
                    radioPrint.isVisible = true
                    edtType.setText(getString(R.string.payment_method_report))
                }

                ReportExportEnum.SALES_REPORT.id -> {

                    radioPrint.isVisible = false
                    radioPrintType.check(R.id.radioExport)
                    edtType.setText(getString(R.string.sales_report))
                }
            }
            showFormatSelect()
            checkBtnEnable()
        }
    }


    private fun showDateTimePicker(editText: EditText, textView: TextView) {
        val calendar: Calendar = Calendar.getInstance()
        val datePickerDialog = context?.let {
            DatePickerDialog(
                it, { _: DatePicker, year: Int, monthOfYear: Int, dayOfMonth: Int ->
                    calendar.set(Calendar.YEAR, year)
                    calendar.set(Calendar.MONTH, monthOfYear)
                    calendar.set(Calendar.DAY_OF_MONTH, dayOfMonth)

                    showTimePicker(calendar, editText, textView)
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH)
            )
        }

        datePickerDialog?.datePicker?.maxDate = System.currentTimeMillis()

        datePickerDialog?.setCancelable(false)
        datePickerDialog?.show()

        datePickerDialog?.getButton(DialogInterface.BUTTON_POSITIVE)?.text =
            getString(R.string.confirm2)
        datePickerDialog?.getButton(DialogInterface.BUTTON_NEGATIVE)?.text =
            getString(R.string.cancel)
    }

    private fun showTimePicker(calendar: Calendar, editText: EditText, textView: TextView) {
        val currentCalendar = Calendar.getInstance()
        val currentHour = currentCalendar.get(Calendar.HOUR_OF_DAY) // 24-hour format
        val currentMinute = currentCalendar.get(Calendar.MINUTE)
        val timePickerDialog = RangeTimePickerDialog(
            context,
            { view, hourOfDay, minute ->
                if (isQuickSelect) {
                    binding?.apply {
                        tvStartTime.text = ""
                        tvEndTime.text = ""
                    }
                }
                isQuickSelect = false

                calendar.set(Calendar.HOUR_OF_DAY, hourOfDay)
                calendar.set(Calendar.MINUTE, minute)
                editText.setText(calendar.time.formatDateStr(FORMAT_DATE_TIME_SHOW))
                textView.text = calendar.time.formatDateStr(FORMAT_DATE_TIME_SHOW)
                binding?.apply {
                    tvToday.isSelected = false
                    tvYesterday.isSelected = false
                    tvLastWeek.isSelected = false
                    tvLastMonth.isSelected = false
                }
                checkBtnEnable()

            },
            currentHour,
            currentMinute,
            true
        )

        timePickerDialog.setCancelable(false)
        timePickerDialog.show()

        timePickerDialog?.getButton(DialogInterface.BUTTON_POSITIVE)?.text =
            getString(R.string.confirm2)
        timePickerDialog?.getButton(DialogInterface.BUTTON_NEGATIVE)?.text =
            getString(R.string.cancel)
    }


//    var dir = FolderHelper.getTempFileDir().absolutePath
//    var fileName = ""


    private fun download(url: String) {
        val fileName = FileUtil.getFileNameInUrl(url)
        val dir = FolderHelper.getDownloadFolderPath()
        DownloadManager.getInstance()
            .download(
                TAG,
                url,
                fileName,
                dir ?: "",
                object : DownloadListener {
                    override fun onProgress(progress: Long, max: Long) {
                        requireActivity().apply {
                            Timber.e("progress $progress")
                            runOnUiThread {
                                binding?.apply {
                                    val currentProgress =
                                        ((progress * 1.0f / max).times(100)).toInt()

                                }
                            }

                        }
                    }

                    override fun onSuccess(localPath: String) {
                        val file = File(dir, fileName)
                        val renameToRes = File(localPath).renameTo(file)
                        FileUtil.scanFile(requireActivity(), file)
                        requireActivity().runOnUiThread {
                            Toast.makeText(
                                requireActivity(),
                                requireActivity().getString(
                                    R.string.save_at_location,
                                    file.absolutePath
                                ),
                                Toast.LENGTH_LONG
                            ).show()
                        }

                        dismissAllowingStateLoss()
                    }

                    override fun onFail(errorInfo: String) {
                        Timber.e("错误")
                        requireActivity().apply {
                            runOnUiThread {
                                binding?.apply {
                                    btnYes.setEnableWithAlpha(true)
                                }
                                if (errorInfo.isNotEmpty())
                                    Toast.makeText(context, errorInfo, Toast.LENGTH_LONG).show()
                            }
                        }
                    }
                })
    }


    companion object {
        private const val TAG = "PrintReportDialog"

        private const val REPORT_TYPE = "report_type"

        fun showDialog(
            fragmentManager: FragmentManager,
            type: Int
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(type)
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? PrintReportDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            type: Int
        ): PrintReportDialog {
            val args = Bundle()
            val fragment = PrintReportDialog()
            args.putInt(REPORT_TYPE, type)
            fragment.arguments = args
            return fragment
        }
    }

}
