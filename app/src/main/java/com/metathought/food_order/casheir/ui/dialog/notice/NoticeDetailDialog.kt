package com.metathought.food_order.casheir.ui.dialog.notice

import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.util.SmartGlideImageLoader
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.NoticeResponse
import com.metathought.food_order.casheir.databinding.DialogNoticeDetailBinding
import com.metathought.food_order.casheir.kotlin_interface.ImageClickInterface
import com.metathought.food_order.casheir.network.ApiResponse
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber


@AndroidEntryPoint
class NoticeDetailDialog : DialogFragment() {
    private var binding: DialogNoticeDetailBinding? = null

    private val viewModel: NoticeViewModel by viewModels()

    private var notice: NoticeResponse? = null


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogNoticeDetailBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)

        initListener()
        initData()
        initObserver()
    }

    private fun initObserver() {

        viewModel.detailState.observe(viewLifecycleOwner) { state ->
            if (state is ApiResponse.Success) {
                binding?.apply {
                    tvTitle.text = state.data?.title
                    tvTime.text = state.data?.createTime
                    progressBar.isVisible = false

                    webView.loadDataWithBaseURL(
                        null,
                        state.data?.content ?: "",
                        "text/html",
                        "UTF-8",
                        null
                    )

                }
            } else if (state is ApiResponse.Error) {
                binding?.apply {
                    progressBar.isVisible = false
                    if (!state.message.isNullOrEmpty())
                        Toast.makeText(requireContext(), "${state.message}", Toast.LENGTH_LONG)
                            .show()
                }

            }

        }
    }


    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.85).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.35).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    private fun initData() {

        binding?.apply {

            viewModel.getNotice(notice!!)

        }

    }

    private fun initListener() {
        binding?.apply {

            btnClose.setOnClickListener {
                dismissAllowingStateLoss()
            }
            tvTitle.text = notice?.title
            tvTime.text = notice?.createTime
//            webView.loadDataWithBaseURL(null, notice?.content ?: "", "text/html", "UTF-8", null)
            webView.settings.javaScriptEnabled = true;
            webView.webViewClient = object : WebViewClient() {
                override fun onPageFinished(view: WebView, url: String) {
                    super.onPageFinished(view, url)
                    // 注入JavaScript代码来自适应图片宽度
                    injectJavaScript(view)
                }
            }
            webView.addJavascriptInterface(ImageClickInterface {
                Timber.e("点击  :${it}")
                XPopup.Builder(requireActivity())
                    .asImageViewer(imageView, it, SmartGlideImageLoader())
                    .isShowSaveButton(false)
                    .show()
            }, "AndroidInterface")
        }
    }

    private fun injectJavaScript(webView: WebView) {
        // 注入JavaScript代码
        val javascript =
            "javascript:document.querySelector('meta[name=\"viewport\"]').content=\"width=device-width, initial-scale=1.0, user-scalable=no\";"
        webView.evaluateJavascript(javascript, null)

        val imgTagCss =
            "javascript:document.querySelectorAll('img').forEach(function (img) { img.style.maxWidth = '100%'; img.style.height = 'auto'; });"
        webView.evaluateJavascript(imgTagCss, null)

        webView?.loadUrl(
            "javascript:(function() {" +
                    "var images = document.getElementsByTagName('img');" +
                    "for (var i = 0; i < images.length; i++) {" +
                    "images[i].onclick = function() {" +
                    "AndroidInterface.onImageClick(this.src);" +
                    "};" +
                    "}" +
                    "})()"
        )

    }

    companion object {
        private const val TAG = "NoticeDetailDialog"


        fun showDialog(
            fragmentManager: FragmentManager,
            notice: NoticeResponse?
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(notice)
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? NoticeDetailDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            notice: NoticeResponse?
        ): NoticeDetailDialog {
            val fragment = NoticeDetailDialog()
            fragment.notice = notice
            return fragment
        }
    }


    private fun getDisplayMetrics(context: Context): DisplayMetrics {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
        return defaultDisplayContext.resources.displayMetrics
    }
}



