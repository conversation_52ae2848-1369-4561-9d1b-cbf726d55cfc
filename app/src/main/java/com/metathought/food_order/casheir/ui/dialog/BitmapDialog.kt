package com.metathought.food_order.casheir.ui.dialog

import android.content.Context
import android.graphics.Bitmap
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.databinding.BitmapLayoutBinding


class BitmapDialog : DialogFragment() {
    private var binding: BitmapLayoutBinding? = null
    private var bitmap: Bitmap? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = BitmapLayoutBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initListener()
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * PERCENT_85).toInt()
            val screenWidth = (displayMetrics.widthPixels * PERCENT_85).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    private fun initData() {

    }

    private fun initListener() {
        binding?.apply {
            ivBitmap.setImageBitmap(bitmap)

            btnCancel.setOnClickListener {
                dismissAllowingStateLoss()
            }
        }
    }

    companion object {
        private const val LOGOUT_DIALOG = "LOGOUT_DIALOG"
        private const val CONTENT = "CONTENT"
        private const val POSITIVE_TITLE_BUTTON = "POSITIVE_TITLE_BUTTON"
        private const val NEGATIVE_TITLE_BUTTON = "NEGATIVE_TITLE_BUTTON"


        // start - viettran1 - AM -133 - 18/07/2023
        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85

        // end - viettran1 - AM -133 - 18/07/2023
        fun showDialog(
            fragmentManager: FragmentManager,
            bitmap: Bitmap? = null,
        ) {
            var fragment = fragmentManager.findFragmentByTag(LOGOUT_DIALOG)
            if (fragment != null) return
            fragment = newInstance(bitmap)
            fragment.show(fragmentManager, LOGOUT_DIALOG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(LOGOUT_DIALOG) as? BitmapDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            bitmap: Bitmap? = null
        ): BitmapDialog {
            val fragment = BitmapDialog()
            fragment.bitmap = bitmap
            return fragment
        }
    }


    private fun getDisplayMetrics(context: Context): DisplayMetrics {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
        return defaultDisplayContext.resources.displayMetrics
    }
}
