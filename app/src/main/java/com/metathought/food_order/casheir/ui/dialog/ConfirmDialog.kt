package com.metathought.food_order.casheir.ui.dialog

import android.content.Context
import android.graphics.Typeface
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.databinding.DialogConfirmBinding


class ConfirmDialog : DialogFragment() {
    private var binding: DialogConfirmBinding? = null
    private var positiveButtonListener: (() -> Unit)? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogConfirmBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initListener()
    }

    override fun onResume() {
        super.onResume()
//        context?.let {
//            val displayMetrics = getDisplayMetrics(it)
//            val screenHeight = (displayMetrics.heightPixels * PERCENT_85).toInt()
//            val screenWidth = (displayMetrics.widthPixels * PERCENT_85).toInt()
//            dialog?.window?.setLayout(screenWidth, screenHeight)
//        }
    }

    private fun initData() {
        val title = arguments?.getString(TITLE, "")
        val content = arguments?.getString(CONTENT)
        val positiveButtonTitle = arguments?.getString(POSITIVE_TITLE_BUTTON)
        val negativeButtonTitle = arguments?.getString(NEGATIVE_TITLE_BUTTON)
        val isShowCancel = arguments?.getBoolean(SHOW_CANCEL)
        binding?.apply {
            if (title?.isNotEmpty() == true) {
                tvTitle.text = title
                tvTitle.isVisible = true
                tvContent.typeface = Typeface.defaultFromStyle(Typeface.NORMAL)
            }

            tvContent.text = content
            positiveButtonTitle?.let {
                btnYes.text = positiveButtonTitle
            }
            negativeButtonTitle?.let {
                btnNo.text = negativeButtonTitle
            }
            isShowCancel?.let {
                btnNo.isVisible = isShowCancel
            }

        }

    }

    private fun initListener() {
        binding?.apply {

            btnNo.setOnClickListener() {
                dismissAllowingStateLoss()
            }
            btnYes.setOnClickListener {
                dismissAllowingStateLoss()
                positiveButtonListener?.invoke()
            }
        }
    }

    companion object {
        private const val LOGOUT_DIALOG = "LOGOUT_DIALOG"
        private const val TITLE = "TITLE"
        private const val CONTENT = "CONTENT"
        private const val POSITIVE_TITLE_BUTTON = "POSITIVE_TITLE_BUTTON"
        private const val NEGATIVE_TITLE_BUTTON = "NEGATIVE_TITLE_BUTTON"
        private const val SHOW_CANCEL = "SHOW_CANCEL"


        // start - viettran1 - AM -133 - 18/07/2023
        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85

        // end - viettran1 - AM -133 - 18/07/2023
        fun showDialog(
            fragmentManager: FragmentManager,
            title: String? = null,
            content: String? = null,
            positiveButtonTitle: String? = null,
            negativeButtonTitle: String? = null,
            isShowCancel: Boolean? = true,
            positiveButtonListener: (() -> Unit),
        ) {
            var fragment = fragmentManager.findFragmentByTag(LOGOUT_DIALOG)
            if (fragment != null) return
            fragment = newInstance(
                positiveButtonListener,
                title,
                content,
                positiveButtonTitle,
                negativeButtonTitle,
                isShowCancel
            )
            fragment.show(fragmentManager, LOGOUT_DIALOG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(LOGOUT_DIALOG) as? ConfirmDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            iAgreeListener: (() -> Unit),
            title: String?,
            content: String? = null,
            positiveButtonTitle: String? = null,
            negativeButtonTitle: String? = null,
            isShowCancel: Boolean? = true,
        ): ConfirmDialog {
            val args = Bundle()
            args.putString(TITLE, title)
            args.putString(CONTENT, content)
            args.putString(POSITIVE_TITLE_BUTTON, positiveButtonTitle)
            args.putString(NEGATIVE_TITLE_BUTTON, negativeButtonTitle)
            args.putBoolean(SHOW_CANCEL, isShowCancel ?: true)
            val fragment = ConfirmDialog()
            fragment.positiveButtonListener = iAgreeListener
            fragment.arguments = args
            return fragment
        }
    }


    private fun getDisplayMetrics(context: Context): DisplayMetrics {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
        return defaultDisplayContext.resources.displayMetrics
    }
}
