package com.metathought.food_order.casheir.ui.widget

import android.content.Context

import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import androidx.core.view.isVisible

import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.order.BaseOrderGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.PromotionActivity
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.databinding.ViewOrderGiftListBinding
import com.metathought.food_order.casheir.databinding.ViewTitleContentTextBinding
import com.metathought.food_order.casheir.extension.setStrokeAndColor
import com.metathought.food_order.casheir.ui.adapter.GiftPromotionGoodListAdapter
import com.metathought.food_order.casheir.ui.adapter.OrderedInfoAdapter
import com.metathought.food_order.casheir.utils.DisplayUtils
import timber.log.Timber

/**
 * 订单满赠 商品列表
 *
 * @constructor
 *
 * @param context
 * @param attrs
 */

class OrderGiftListView(context: Context, attrs: AttributeSet? = null) :
    LinearLayout(context, attrs) {
    private var _binding: ViewOrderGiftListBinding? = null
    private var contentPaddingHorizontal = 0f

    init {
        // 取得属性值数组
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.OrderGiftView)
        try {
            // 获取尺寸属性
            contentPaddingHorizontal = typedArray.getDimension(
                R.styleable.OrderGiftView_content_padding_horizontal, 0f
            )
        } catch (e: Exception) {

        }


        // 最后需要回收数组
        typedArray.recycle()

        initView()

    }

    private var adapter = GiftPromotionGoodListAdapter(arrayListOf())
    private var isInit = false

    private fun initView() {
        _binding = ViewOrderGiftListBinding.inflate(LayoutInflater.from(context), this, true)
        _binding?.apply {
            tvTitle.setOnClickListener {
                rvList.isVisible = !rvList.isVisible
                updateArrow()
            }
            layoutContent.setPadding(
                contentPaddingHorizontal.toInt(),
                0,
                contentPaddingHorizontal.toInt(),
                0
            )
        }
    }


    private fun updateArrow() {
        _binding?.apply {
            ivArrow.setImageResource(if (rvList.isVisible) R.drawable.icon_arrow_up else R.drawable.icon_arrow_down)
        }
    }

//    fun setGiftListData(list: ArrayList<BaseOrderGoods>?) {
//        _binding?.apply {
//            Timber.e("初始化")
//            if (!isInit) {
//                rvList.adapter = orderedInfoAdapter
//                isInit = true
//            }
//            rvList.isVisible = true
//            updateArrow()
//        }
//        Timber.e("list  ${list?.size}")
//        orderedInfoAdapter.replaceData(newData = list)
//
//    }

    fun setGiftPromotionData(mContext: Context, promotionActivity: PromotionActivity?) {
        _binding?.apply {
            Timber.e("初始化")
            if (!isInit) {
                rvList.adapter = adapter
                isInit = true
            }
            rvList.isVisible = true
            updateArrow()

            tvCount.text =
                mContext.getString(R.string.total_good_num, "${promotionActivity?.giftNum ?: 0}")
            if (promotionActivity != null && promotionActivity.labelColor != null) {
                tvGiftActivityTag.setStrokeAndColor(color = promotionActivity.labelColor.toColorInt())
                tvGiftActivityTag.setTextColor(promotionActivity.labelColor.toColorInt())
                tvGiftActivityTag.text = promotionActivity.labelName
            }
        }

        adapter.updateItems(mContext, newItems = promotionActivity?.giftGoods)
    }


}