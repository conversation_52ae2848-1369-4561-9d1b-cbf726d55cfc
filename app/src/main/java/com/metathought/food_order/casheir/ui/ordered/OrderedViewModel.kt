package com.metathought.food_order.casheir.ui.ordered

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.pdf.PdfDocument
import android.print.PrintAttributes
import android.widget.Toast
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.DiningStyleEnum
import com.metathought.food_order.casheir.constant.FORMAT_DATE_EXPORT_NAME
import com.metathought.food_order.casheir.constant.KIOSK
import com.metathought.food_order.casheir.constant.KitchenCheckTicketType
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.constant.PrintTemplateTypeEnum
import com.metathought.food_order.casheir.constant.PrintTicketType
import com.metathought.food_order.casheir.data.CashConvertModel
import com.metathought.food_order.casheir.data.model.base.BaseBooleanResponse
import com.metathought.food_order.casheir.data.model.base.request_model.AddOrderMoreGoodRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ConsumerPayRegisterInfo
import com.metathought.food_order.casheir.data.model.base.request_model.EditDeliveryOrderNoRequest
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsBo
import com.metathought.food_order.casheir.data.model.base.request_model.PaymentRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ReserveTableRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.DeleteGoodsNumRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.DiscountInfo
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.OrderListRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.PartialRefundRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.PayAgainRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.PreSettlementRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.PrinterAgainRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.RefundGoods
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.RemoveGoodsBo
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.SingleDiscountRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.UpdateCustomerInfoRequest
import com.metathought.food_order.casheir.data.model.base.request_model.tableServiceRequest.ConfirmPendingGoodsRequest
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponModel
import com.metathought.food_order.casheir.data.model.base.response_model.member.MemberListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelModel
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.order.PaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.ConsumerResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedRecord
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTableListItem
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTableListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTotalResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.PaymentChannelModel
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.TableOrderStatus
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.WsOrderPreSettlementChangeResponse
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrintTamplateResponseItem
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrinterTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponseItem
import com.metathought.food_order.casheir.database.ShoppingRecord
import com.metathought.food_order.casheir.database.dao.HashHelper
import com.metathought.food_order.casheir.database.dao.ShoppingHelper
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import com.metathought.food_order.casheir.extension.formatDateStr
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.helper.FolderHelper
import com.metathought.food_order.casheir.helper.OrderHelper
import com.metathought.food_order.casheir.helper.PaymentMethodHelper
import com.metathought.food_order.casheir.helper.PrinterDeviceHelper
import com.metathought.food_order.casheir.listener.ListenableFuture
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.GOODS_HAS_BEEN_MERGE_OR_SPLIT
import com.metathought.food_order.casheir.network.Repository
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.member.MemberMainViewModel
import com.metathought.food_order.casheir.ui.ordered.printer.PrinterConfirmViewModel
import com.metathought.food_order.casheir.ui.widget.printer.LabelPrinter
import com.metathought.food_order.casheir.ui.widget.printer.Printer
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream
import java.math.BigDecimal
import java.util.Date
import java.util.concurrent.ExecutionException
import javax.inject.Inject


@HiltViewModel
class OrderedViewModel @Inject
constructor(private val repository: Repository) : ViewModel() {

    val uiInfoRequestState get() = _uiInfoRequestState
    private val _uiInfoRequestState = MutableLiveData<UIInfoRequestModel>()

    val uiListState get() = _uiListState
    private val _uiListState = MutableLiveData<UIListModel>()

    val uiRequestState get() = _uiRequestState
    private val _uiRequestState = MutableLiveData<UIRequestState>()

    val uiInfoState get() = _uiInfoState
    private val _uiInfoState = MutableLiveData<UIInfoModel>()

    val uiSocketOrderedState get() = _uiSocketOrderedState
    private val _uiSocketOrderedState = MutableLiveData<OrderedRecord>()


    val uiTableState get() = _uiTableState
    private val _uiTableState = MutableLiveData<UITableStateModel>()

    private val _tableResponse = MutableLiveData<List<OrderedTableListItem>>()
//    val tableResponse get() = _tableResponse

    private val _filteredTableResponse = MutableLiveData<List<OrderedTableListItem>>()
    val filteredTableResponse get() = _filteredTableResponse


    val uiChangeTableByOrderState get() = _uiChangeTableByOrderState
    private val _uiChangeTableByOrderState =
        MutableLiveData<UIChangeTableByOrder>()


    private val _paymentChannel = MutableLiveData<List<PaymentChannelModel>>()
    val paymentChannel get() = _paymentChannel

    val uiSplitOrderState get() = _uiSplitOrderState
    private val _uiSplitOrderState = MutableLiveData<ApiResponse<BaseBooleanResponse>>()

    private var pagNo = 1
    private val pageSize = 50
    var currentOrderedInfo: OrderedInfoResponse? = null
    var currentCouponInfo: CouponModel? = null
    var orderRecord: OrderedRecord? = null
    private val _uiSaveRecordState = MutableLiveData<UISaveRecordStateModel>()


    //当前订单
    private var currentOrderUsableCouponList = listOf<CouponModel>()

    fun getOrderUsableCouponList(): List<CouponModel> {
        return currentOrderUsableCouponList
    }

    val uiSaveRecordState get() = _uiSaveRecordState

    /**
     * Get ordered list
     *
     * @param isRefresh
     * @param payStatus
     * @param keyword
     * @param listTable
     * @param currentOrderMore
     * @param confirmOrderNo  确认的订单号
     * @param localOrderNo  定位到的位置
     */
    fun getOrderedList(
        isRefresh: Boolean? = null,
        payStatus: Int? = null,
        keyword: String? = null,
        listTable: ArrayList<OrderedTableListItem>? = null,
        currentOrderMore: ArrayList<GoodsBo>? = null,
        confirmOrderNo: String? = null,
        localOrderNo: String? = null, //定位到的位置
        isNeedPrint: Boolean? = false, //是否需要打印
        isFilterUnRead: Boolean? = false, //是否只展示未读
        isFilterUnPrint: Boolean? = false, //是否只展示未打印
        finishPay: Boolean? = false, //
        consumerId: String? = null, //
    ) {
        viewModelScope.launch(Dispatchers.IO) {

            if (isRefresh != false) {
                pagNo = 1
                if (uiListState.value?.showLoading == true) {
                    //防止刷新的请求太多次
                    return@launch
                }
                if (isRefresh == null) {
                    withContext(Dispatchers.Main) {
                        emitUIListState(showLoading = true)
                    }
                }
                val hasOrder = !confirmOrderNo.isNullOrEmpty()
                if (hasOrder) {
                    //可能出现确认完订单，order/list 还没有出现这笔订单的情况，后端需要解决这个问题
                    //It may happen that after the order is confirmed, the order does not appear in the order/list. The backend needs to solve this problem
                    delay(500)
                }
            }


            try {
                withContext(Dispatchers.Main) {
                    EventBus.getDefault().post(SimpleEvent(SimpleEventType.GET_UNREAD_EVENT, null))
                }
                var type: Int? = null
                if (isFilterUnRead == true && isFilterUnPrint == false) {
                    type = 1
                } else if (isFilterUnRead == false && isFilterUnPrint == true) {
                    type = 2
                }
                val orderListRequest = OrderListRequest(
                    page = pagNo,
                    pageSize = pageSize,
                    keyword = keyword,
                    type = type,
                    consumerId = consumerId,
                )
                payStatus?.let {
                    if (payStatus != -1)
                        orderListRequest.status = payStatus
                }
                listTable?.let {
                    if (listTable.isNotEmpty()) {
                        val arrayTable = mutableListOf<String>()
                        var isHasKiosk = false
                        for (table in listTable) {
                            table.uuid?.let { id ->
                                if (table.uuid != KIOSK) {
                                    arrayTable.add(id)
                                } else {
                                    isHasKiosk = true
                                }
                            }

                        }
                        orderListRequest.isKioskOrder = isHasKiosk
                        orderListRequest.tableUuids = arrayTable
                    }
                }
                val result = repository.orderListV2(
                    orderListRequest
                )
                withContext(Dispatchers.Main) {
                    if (result is ApiResponse.Success) {
                        val response = result.data
                        if (response.records.isNullOrEmpty()) {
                            emitUIListState(
                                showEnd = true,
                                isRefresh = isRefresh,
                                showLoading = false
                            )
                            return@withContext
                        }
                        pagNo++
                        Timber.e("列表请求完后 confirmOrderNo:${confirmOrderNo}  localOrderNo:${localOrderNo}")
                        emitUIListState(
                            showSuccess = response,
                            isRefresh = isRefresh,
                            showLoading = false,
                            confirmOrderNo = confirmOrderNo,
                            currentOrderMore = currentOrderMore,
                            localOrderNo = localOrderNo, //定位到的位置
                            isNeedPrint = isNeedPrint, //是否需要打印
                            finishPay = finishPay
                        )

                    } else if (result is ApiResponse.Error) {
                        emitUIListState(showError = result.message, showLoading = false)
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    emitUIListState(showError = e.message, showLoading = false)
                }
            }
        }
    }

    /**
     * orderMoreNo不为空则需要走打印
     *
     * @param orderNo    订单号
     * @param context
     * @param isOrderMore  是否加购
     * @param finishPay   是否结束支付
     * @param weightGoods  称重商品
     * @param isNeedPrint  是否需要打印
     */
    fun requestOrderedInfo(
        orderNo: String?,
        context: Context? = null,
        isOrderMore: Boolean? = false,
        finishPay: Boolean = false,
        weightGoods: OrderedGoods? = null,
        isNeedPrint: Boolean? = false
    ) {
        //判断一下当前列表选中的是不是要请求的订单
        if (orderRecord != null && orderRecord?.orderNo == orderNo) {
            getOrderedInfo(
                orderRecord!!,
                finishPay = finishPay,
                context = context,
                weightGoods = weightGoods,
                isNeedPrint = isNeedPrint
            )
        }
    }

    /**
     * orderMoreNo如果不为空，则需要打印加购订单
     *
     * @param record
     * @param context
     * @param orderNo
     * @param currentOrderMore
     * @param weightGoods
     * @param finishPay
     * @param isRead
     * @param isNeedPrint
     */
    fun getOrderedInfo(
        record: OrderedRecord,
        context: Context? = null,
        orderNo: String? = null,
        currentOrderMore: ArrayList<GoodsBo>? = null,
        weightGoods: OrderedGoods? = null,
        finishPay: Boolean = false,
        isRead: Boolean? = false,
        isNeedPrint: Boolean? = false
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            withContext(Dispatchers.Main) {
                emitUIInfoState(orderedInfoResponse = ApiResponse.Loading)
            }
            try {
                //todo   这个需要处理一下如果
                orderRecord = record
                val orderedInfo = repository.orderedInfo(record.orderNo, isRead)
                currentOrderedInfo = if (orderedInfo is ApiResponse.Success) {
                    orderedInfo.data
                } else {
                    null
                }
                if (orderedInfo is ApiResponse.Error) {
                    withContext(Dispatchers.Main) {
                        emitUIInfoState(orderedInfoResponse = orderedInfo, record = null)
                    }
                }

                if (isRead == true && record.isRead == false) {
                    //未读的时候调用下未读数接口
                    withContext(Dispatchers.Main) {
                        EventBus.getDefault()
                            .post(SimpleEvent(SimpleEventType.GET_UNREAD_EVENT, null))
                    }
                }

                if (currentOrderedInfo?.isAfterPayStatus() == false) {
                    //如果请求的订单是非支付状态  清空本地缓存记录
                    PrinterDeviceHelper.clearOrderPrinter(
                        orderNo = currentOrderedInfo!!.orderNo
                    )
                }

                var item: OrderedRecord? = null
                //判断一下请求回来的是否当前列表选择的
                if (orderRecord?.orderNo == currentOrderedInfo?.orderNo) {
                    if (currentOrderedInfo?.isAfterPayStatus() == false) {
                        //如果请求的订单是非支付状态  清空本地缓存记录
                        PrinterDeviceHelper.clearOrderPrinter(
                            orderNo = currentOrderedInfo!!.orderNo
                        )
                    }
                    record.payStatus = currentOrderedInfo?.payStatus
                    record.realPrice = currentOrderedInfo?.getFinalRealPrice()

                    record.tableUuid = currentOrderedInfo?.tableUuid
                    record.tableName = currentOrderedInfo?.tableName
                    record.tableType = currentOrderedInfo?.tableType
                    record.acceptStatus = currentOrderedInfo?.acceptStatus
                    record.expireCountDown = currentOrderedInfo?.waitAcceptAddOrder?.expireCountDown
                    if (isRead == true) {
                        record.isRead = true
                    }
                    if (currentOrderedInfo?.needResetUnReadAndPrintStatus() == true) {
                        record.isPrinted = true
                        record.isRead = true
                    }

                    record.isOrdersWeightMark = currentOrderedInfo?.isHasNeedProcess()
                    record.goodsTotalNum = currentOrderedInfo?.getTotalGoodsNum()

                    currentOrderMore?.let {
                        val list = ArrayList<OrderedGoods>()
                        currentOrderMore.forEach { bo ->
                            //可能会有同一到待称重的菜，一个没称重，一个带称重
                            val first = currentOrderedInfo?.goods?.firstOrNull {
                                HashHelper.getHashByTagStr(
                                    ArrayList(bo.feeds ?: listOf()),
                                    bo.tagItemId?.replace(",", "") ?: "",
                                    bo.mealSetGoodsDTOList,
                                    bo.id!!,
                                    singleItemDiscount = bo.singleDiscountGoods,
                                    goodsPriceKey = "",
                                    note = bo.note,
                                    uuid = bo.uuid,
                                ) == HashHelper.getHash(
                                    ArrayList(it.feeds ?: listOf()),
                                    ArrayList(it.tagItems ?: listOf()),
                                    it.orderMealSetGoodsDTOList,
                                    it.id!!,
                                    singleItemDiscount = it.singleItemDiscount?.toCartSingleDiscountGood(),
                                    goodsPriceKey = "",
                                    note = it.note,
                                    uuid = it.uuid,
                                )
                            }?.copy()
                            first?.let {
                                list.add(it.apply {
//                                    weight = null
//                                    weighingCompleted = false
                                    num = bo.num
                                })
                            }
                        }
                        //可能出现已经被过滤掉待称重的菜，所以此时只有currentOrderMore有值，list为空
                        currentOrderedInfo?.currentOrderMoreList = list
                    }

                    weightGoods?.let {
                        //将称重的加到加够里面继续打印
                        val list = ArrayList<OrderedGoods>()
                        list.add(it)
                        currentOrderedInfo?.currentOrderMoreList = list
                    }

                    item = record
                    withContext(Dispatchers.Main) {
                        emitUIInfoState(orderedInfoResponse = orderedInfo, record = item)
                    }
                    getAvailableCouponList()
                }

                Timber.e("finishPay  $finishPay   orderNo:${orderNo}  isNeedPrint:$isNeedPrint")
                if (isNeedPrint == true) {
                    if (finishPay) {
                        context?.let {
                            //这个判断是不要厨打（和服务端一样的逻辑）
                            val autoCheckoutTicket =
                                MainDashboardFragment.STORE_INFO?.autoCheckoutTicket()
                            if (MainDashboardFragment.CURRENT_USER?.isPaymentInAdvance == false && currentOrderedInfo?.tableType == 1 && currentOrderedInfo?.diningStyle == DiningStyleEnum.DINE_IN.id && (currentOrderedInfo?.payStatus == OrderedStatusEnum.PAID.id || currentOrderedInfo?.payStatus == OrderedStatusEnum.CREDIT_UNPAID.id)) {
                                // 后付款结账 不打厨打
                                printerTemplate(
                                    it,
                                    currentOrderedInfo!!,
                                    printTicketType = if (autoCheckoutTicket == true) Printer.getPrintTicketType() else PrintTicketType.NONE.id,
                                )
                            } else {
                                printerTemplate(
                                    it,
                                    currentOrderedInfo!!,
                                    isKitchen = true,
                                    printTicketType = if (autoCheckoutTicket == true) Printer.getPrintTicketType() else PrintTicketType.NONE.id,
                                    isPrintLabel = true
                                )
                            }
                        }
                        //已确认变支付，不打印厨打
                    } else {
                        //待确认做确认操作
                        if (currentOrderedInfo?.payStatus == OrderedStatusEnum.BE_CONFIRM.id) {
                            context?.let {
                                printerTemplate(
                                    it,
                                    currentOrderedInfo!!,
                                    isKitchen = true,
                                    printTicketType = PrintTicketType.NORMAL.id,
                                    isPrintLabel = true

                                )
                            }
                        }
                        //待支付订单 后付款堂食 称重完打印 小票
                        if (currentOrderedInfo?.payStatus == OrderedStatusEnum.UNPAID.id && currentOrderedInfo?.diningStyle == DiningStyleEnum.DINE_IN.id && MainDashboardFragment.CURRENT_USER?.isPaymentInAdvance != true && currentOrderedInfo?.isUniversalTable() == false) {
                            context?.let {
                                printerTemplate(
                                    it,
                                    currentOrderedInfo!!,
                                    isKitchen = true,
                                    printTicketType = PrintTicketType.NORMAL.id,
                                    isPrintLabel = true
                                )
                            }
                        }

                    }
                }

            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    emitUIInfoState(orderedInfoResponse = ApiResponse.Error(e.message))
                }
                e.printStackTrace()
            } finally {
                //打印完成，清空加购的数据 Printing completed, clear the purchased data
                if (currentOrderedInfo?.currentOrderMoreList?.isNotEmpty() == true) {
                    currentOrderedInfo?.currentOrderMoreList?.clear()
                }
            }

        }
    }


    /**
     * 通过ws 更新订单列表信息
     *
     * @param orderRecord
     */
    fun websocketRefreshOrder(orderRecord: OrderedRecord) {
        if (currentOrderedInfo?.orderNo == orderRecord.orderNo) {
            getOrderedInfo(orderRecord)
        } else {
            viewModelScope.launch(Dispatchers.IO) {
                try {
                    val orderedInfo = repository.orderedInfo(orderNo = orderRecord.orderNo)
                    if (orderedInfo is ApiResponse.Success) {
                        if (orderedInfo.data?.isAfterPayStatus() == false) {
                            //如果请求的订单是非支付状态  清空本地缓存记录
                            PrinterDeviceHelper.clearOrderPrinter(
                                orderNo = currentOrderedInfo!!.orderNo
                            )
                        }
                        orderRecord?.payStatus = orderedInfo?.data?.payStatus
                        orderRecord?.realPrice = orderedInfo?.data?.getFinalRealPrice()

                        orderRecord?.tableUuid = orderedInfo?.data?.tableUuid
                        orderRecord?.tableName = orderedInfo?.data?.tableName
                        orderRecord?.tableType = orderedInfo?.data?.tableType
                        orderRecord.expireCountDown =
                            orderedInfo?.data?.waitAcceptAddOrder?.expireCountDown
                        orderRecord?.acceptStatus = orderedInfo?.data?.acceptStatus
                        orderRecord.isOrdersWeightMark = orderedInfo.data.isHasNeedProcess()
                        orderRecord.goodsTotalNum = orderedInfo.data.getTotalGoodsNum()

                        if (orderedInfo.data.needResetUnReadAndPrintStatus()) {
                            orderRecord.isPrinted = true
                            orderRecord.isRead = true
                        }


                        val shoppingRecord =
                            ShoppingHelper.get(orderedInfo?.data?.diningStyle!!)

                        if (shoppingRecord?.orderMoreID == orderedInfo?.data?.orderNo) {
                            ShoppingHelper.updateSelectTable(
                                orderedInfo?.data?.tableUuid,
                                orderedInfo?.data?.tableName,
                                orderedInfo?.data?.tableType,
                                orderedInfo?.data?.diningStyle!!
                            )
                        }
                        withContext(Dispatchers.Main) {
                            _uiSocketOrderedState.postValue(orderRecord)
                        }
                    }

                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }


    /**
     * 获取订单信息
     *
     * @param orderNo
     */
    fun getOrderedInfo(orderNo: String) {
        viewModelScope.launch(Dispatchers.IO) {
            withContext(Dispatchers.Main) {
                emitUIInfoState(orderedInfoResponse = ApiResponse.Loading)
            }

            try {
                val response = repository.orderedInfo(orderNo = orderNo)

                var item: OrderedRecord? = null
                if (response is ApiResponse.Success) {
                    if (!response.data.isAfterPayStatus()) {
                        PrinterDeviceHelper.clearOrderPrinter(
                            orderNo = response.data.orderNo
                        )
                    }
                    //如果列表选择的是请求的订单
                    if (orderRecord?.orderNo == orderNo) {
                        Timber.e("还是当前选中的")
                        currentOrderedInfo = response.data
                        if (orderRecord != null) {
                            orderRecord?.payStatus = currentOrderedInfo?.payStatus
                            orderRecord?.realPrice = currentOrderedInfo?.getFinalRealPrice()

                            orderRecord?.tableUuid = currentOrderedInfo?.tableUuid
                            orderRecord?.tableName = currentOrderedInfo?.tableName
                            orderRecord?.tableType = currentOrderedInfo?.tableType
                            orderRecord?.expireCountDown =
                                currentOrderedInfo?.waitAcceptAddOrder?.expireCountDown
                            orderRecord?.acceptStatus = currentOrderedInfo?.acceptStatus

                            orderRecord?.isOrdersWeightMark =
                                currentOrderedInfo?.isHasNeedProcess()
                            orderRecord?.goodsTotalNum = currentOrderedInfo?.getTotalGoodsNum()

                            if (currentOrderedInfo?.needResetUnReadAndPrintStatus() == true) {
                                orderRecord?.isPrinted = true
                                orderRecord?.isRead = true
                            }

                            item = orderRecord

                            val shoppingRecord =
                                ShoppingHelper.get(currentOrderedInfo?.diningStyle!!)

                            if (shoppingRecord?.orderMoreID == currentOrderedInfo?.orderNo) {
                                ShoppingHelper.updateSelectTable(
                                    currentOrderedInfo?.tableUuid,
                                    currentOrderedInfo?.tableName,
                                    currentOrderedInfo?.tableType,
                                    currentOrderedInfo?.diningStyle!!
                                )
                            }
                        }
                        getAvailableCouponList()
                        withContext(Dispatchers.Main) {
                            emitUIInfoState(orderedInfoResponse = response, record = item)
                        }
                    } else {
                        val index =
                            uiListState.value?.showSuccess?.records?.indexOfFirst { it.orderNo == orderNo }
                                ?: -1
                        Timber.e("查询完后不是当前列表选中的了 $index")
                        if (index != -1) {
                            item = uiListState.value?.showSuccess?.records?.get(index)
                            item?.payStatus = response.data?.payStatus
                            item?.realPrice = response.data?.getFinalRealPrice()

                            item?.tableUuid = response.data?.tableUuid
                            item?.tableName = response.data?.tableName
                            item?.tableType = response.data?.tableType
                            item?.expireCountDown =
                                response.data?.waitAcceptAddOrder?.expireCountDown
                            item?.acceptStatus = response.data?.acceptStatus

                            item?.isOrdersWeightMark = response.data?.isHasNeedProcess()
                            item?.goodsTotalNum = response.data?.getTotalGoodsNum()

                            if (response.data?.needResetUnReadAndPrintStatus() == true) {
                                item?.isPrinted = true
                                item?.isRead = true
                            }
                        }
                        withContext(Dispatchers.Main) {
                            emitUIInfoRequestState(response)
                            emitUIInfoState(orderedInfoResponse = null, record = item)
                        }
                    }
                    EventBus.getDefault()
                        .post(SimpleEvent(SimpleEventType.GET_ACCEPT_ORDER_UNREAD_EVENT, null))
                } else if (response is ApiResponse.Error) {
                    currentOrderedInfo = null
                    withContext(Dispatchers.Main) {
                        emitUIInfoState(
                            orderedInfoResponse = ApiResponse.Error(
                                response.message,
                                errorCode = response.errorCode
                            )
                        )
                    }
                }

            } catch (e: Exception) {
                emitUIInfoState(orderedInfoResponse = ApiResponse.Error(e.message))
                e.printStackTrace()
            }
        }
    }

    /**
     * 拆单
     *
     * @param orderNo
     */
    fun splitOrder(orderNo: String?) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val response = repository.splitOrder(orderNo)
                withContext(Dispatchers.Main) {
                    _uiSplitOrderState.postValue(response)
                }
            } catch (e: Exception) {

            }
        }
    }

    /**
     * 门店数据那边请求详情用
     *
     * @param orderNo
     */
    fun getOrderedInfoInStoreInfo(orderNo: String) {
        viewModelScope.launch {
            emitUIInfoState(orderedInfoResponse = ApiResponse.Loading)
            try {
                val response = repository.orderedInfo(orderNo = orderNo)

                var item: OrderedRecord? = null
                if (response is ApiResponse.Success) {
                    //如果列表选择的是请求的订单

                    if (!response.data.isAfterPayStatus()) {
                        //如果请求的订单是非支付状态  清空本地缓存记录
                        PrinterDeviceHelper.clearOrderPrinter(
                            orderNo = response.data.orderNo
                        )
                    }
                    Timber.e("还是当前选中的")
                    currentOrderedInfo = response.data

                    getAvailableCouponList()
                    emitUIInfoState(orderedInfoResponse = response, record = item)
                    EventBus.getDefault()
                        .post(SimpleEvent(SimpleEventType.GET_ACCEPT_ORDER_UNREAD_EVENT, null))
                } else if (response is ApiResponse.Error) {
                    currentOrderedInfo = null
                    emitUIInfoState(orderedInfoResponse = ApiResponse.Error(response.message))
                }

            } catch (e: Exception) {
                emitUIInfoState(orderedInfoResponse = ApiResponse.Error(e.message))
                e.printStackTrace()
            }
        }
    }

    /**
     * 更新订单优惠券
     */
    fun updateCouponInfo(couponModel: CouponModel?) {
        viewModelScope.launch(Dispatchers.IO) {
            // 手动选
            currentCouponInfo = couponModel
            if (couponModel == null) {
                currentOrderedInfo?.coupon = null
                currentOrderedInfo?.couponId = null
                currentOrderedInfo?.couponAmount = null
                currentOrderedInfo?.price?.totalCouponAmount = null
                currentOrderedInfo?.vipPrice?.totalCouponAmount = null
            } else {
                currentOrderedInfo?.coupon = couponModel.toOrderCoupon()
                currentOrderedInfo?.couponId = couponModel.id
                currentOrderedInfo?.couponAmount =
                    currentOrderedInfo?.getCurrentCoupon()?.couponPrice
                currentOrderedInfo?.price?.totalCouponAmount =
                    BigDecimal(currentOrderedInfo?.getCurrentCoupon()?.couponPrice ?: 0).divide(
                        BigDecimal(100.0)
                    )
                currentOrderedInfo?.vipPrice?.totalCouponAmount = BigDecimal(
                    currentOrderedInfo?.getCurrentCoupon()?.vipCouponPrice ?: 0
                ).divide(BigDecimal(100.0))
            }

            //修改优惠券以后 要清除本地减免折扣
//            currentOrderedInfo?.reduceKhr = null
//            currentOrderedInfo?.reduceDollar = null
//            currentOrderedInfo?.reduceAmount = null
//            currentOrderedInfo?.reduceRate = null
//            currentOrderedInfo?.reduceType = null
//            currentOrderedInfo?.price?.wholeDiscountReduce = null
//            currentOrderedInfo?.vipPrice?.wholeDiscountReduce = null
//            currentOrderedInfo?.reduceReason = null
            currentOrderedInfo?.price?.totalWholeDiscountAmount = null
            currentOrderedInfo?.vipPrice?.totalWholeDiscountAmount = null
            currentOrderedInfo?.wholeDiscountReduce = null
            currentOrderedInfo?.discountReduceActivity = null

            currentOrderedInfo?.price?.reCalculateAfterChooseCoupon()
            currentOrderedInfo?.vipPrice?.reCalculateAfterChooseCoupon()

            currentOrderedInfo?.realPrice = currentOrderedInfo?.getTotalPriceBySelf()

            if ((currentOrderedInfo?.realPrice ?: 0L) < 0L) {
                currentOrderedInfo?.realPrice = 0L
            }
            orderRecord?.realPrice =
                currentOrderedInfo?.realPrice

            if (currentOrderedInfo != null) {
                withContext(Dispatchers.Main) {
                    emitUIInfoState(
                        orderedInfoResponse = ApiResponse.Success(currentOrderedInfo!!),
                        record = orderRecord
                    )
                }
            }
        }
    }


    /**
     * 获取订单可用优惠券列表
     */
    private fun getAvailableCouponList() {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                if (currentOrderedInfo?.isAfterPayStatus() == true) {
                    return@launch
                }
                val goodsVoList = currentOrderedInfo?.goods
                val goodsBoList = OrderHelper.getGoodsBoList(goodsVoList)
                val requestOrderNo = currentOrderedInfo?.orderNo
                val response = repository.findOrderCanUseCoupon(
                    diningStyle = currentOrderedInfo?.diningStyle,
                    isPreOrder = currentOrderedInfo?.isPreOrder(),
                    tableUuid = currentOrderedInfo?.tableUuid,
                    orderNo = currentOrderedInfo?.orderNo,
                    goodsList = goodsBoList
                )

                if (response is ApiResponse.Success) {
                    if (requestOrderNo == currentOrderedInfo?.orderNo) {
                        currentOrderUsableCouponList = response.data
                    }

                } else {
                    if (requestOrderNo == currentOrderedInfo?.orderNo) {
                        currentOrderUsableCouponList = listOf<CouponModel>()
                    }
                }
                withContext(Dispatchers.Main) {
                    emitUIInfoState(
                        orderedInfoResponse = ApiResponse.Success(currentOrderedInfo!!),
                    )
                }
            } catch (e: Exception) {

            }
        }
    }


    /**
     * 再次付款
     */
    fun payAgain(
        payType: PayTypeEnum,
        accountId: String? = null,
        offlineChannelModel: OfflineChannelModel? = null,
        cashConvert: CashConvertModel? = null,
        nickName: String? = null,
        telephone: String? = null,
        reason: String? = null,
    ) {
        val connectUSD = Printer.isPosPrinterConnectUSB()
        connectUSD.addListener(object : ListenableFuture.Listener<Boolean> {
            override fun onSuccess(result: Boolean) {
                if (currentOrderedInfo != null) {
                    viewModelScope.launch {
                        emitUIRequestState(paymentResponse = ApiResponse.Loading)
                        try {
                            val response = repository.orderedPayAgain(
                                PayAgainRequest(
                                    isPosPrint = result,
                                    payType = payType.id,
                                    accountId = accountId,
                                    orderNo = currentOrderedInfo?.orderNo,
                                    channelsId = offlineChannelModel?.id,
                                    channelsName = offlineChannelModel?.channelsName,
                                    collectCash = cashConvert?.collectCash,
                                    changeAmount = cashConvert?.changeAmount,
                                    collectCashDollar = cashConvert?.collectCashDollar,
                                    changeAmountDollar = cashConvert?.changeAmountDollar,
                                    couponId = currentOrderedInfo?.getCurrentCoupon()?.id,
                                    discount = DiscountInfo(
                                        reduceType = currentOrderedInfo?.getWholeDiscountType(),
                                        reduceRate = currentOrderedInfo?.wholeDiscountReduce?.reduceRate,
                                        reduceDollar = currentOrderedInfo?.wholeDiscountReduce?.reduceDollar,
                                        reduceKhr = currentOrderedInfo?.wholeDiscountReduce?.reduceKhr,
                                        reduceVipDollar = currentOrderedInfo?.wholeDiscountReduce?.reduceVipDollar,
                                        reduceVipKhr = currentOrderedInfo?.wholeDiscountReduce?.reduceVipKhr,
                                        discountReduceActivityId = currentOrderedInfo?.discountReduceActivity?.id,
                                        reduceReason = currentOrderedInfo?.getWholeDiscountReason()
                                    )
                                ).apply {
                                    if (payType == PayTypeEnum.CREDIT) {
                                        isCredit = true
                                        consumerPayRegisterInfo = ConsumerPayRegisterInfo(
                                            telephone = telephone,
                                            nickName = nickName,
                                        )
                                        creditReason = reason
                                    }
                                },
                                payType = payType.id
                            )
                            if (response is ApiResponse.Success) {
                                if (response.data == null) {
                                    //0圆购的时候服务端反的null
                                    emitUIRequestState(
                                        paymentResponse = ApiResponse.Success(
                                            PaymentResponse(
                                                orderNo = currentOrderedInfo?.orderNo,
                                                payType = PayTypeEnum.CASH_PAYMENT.id
                                            )
                                        )
                                    )
                                } else {
                                    emitUIRequestState(paymentResponse = response)
                                }
                            } else {
                                emitUIRequestState(paymentResponse = response)
                            }

                        } catch (e: Exception) {
                            emitUIRequestState(paymentResponse = ApiResponse.Error(e.message))
                        }
                    }
                }
            }

            override fun onFailure(e: ExecutionException) {

            }

        })
    }


    /**
     * 退款
     */
    fun refund(
        orderNo: String?,
        payType: PayTypeEnum,
        fundGoods: ArrayList<OrderedGoods>? = null,
        autoInStock: Boolean
    ) {
        viewModelScope.launch {
            emitUIRequestState(refundResponse = ApiResponse.Loading)
            try {
                val response = if (fundGoods != null) {
                    val list = ArrayList<RefundGoods>()
                    fundGoods.filter { (it.refundsNum ?: 0) > 0 }.forEach {
                        list.add(
                            RefundGoods(
                                num = it.refundsNum,
                                goodSkuKey = it.getHash()
                            )
                        )
                    }

                    repository.partialRefund(
                        PartialRefundRequest(
                            orderId = orderNo,
                            payType = payType.id,
                            refundGoodsList = list,
                            autoInStock = autoInStock
                        )
                    )
                } else {
                    repository.fullRefund(
                        orderNo,
//                        if (payType == PayTypeEnum.PAY_OTHER) PayTypeEnum.CASH_PAYMENT.id else payType.id,
                        payType.id,
                        autoInStock = autoInStock
                    )
                }
                if (response is ApiResponse.Success) {
                    requestOrderedInfo(orderNo)
                } else if (response is ApiResponse.Error) {
                    getOrderedInfo(orderNo ?: "")
                }
                Timber.e("退款")
                EventBus.getDefault().post(SimpleEvent(SimpleEventType.GET_UNREAD_EVENT, null))

                emitUIRequestState(refundResponse = response)
            } catch (e: Exception) {
                emitUIRequestState(refundResponse = ApiResponse.Error(e.message))

            }
        }
    }


    /**
     * 获取桌台列表
     */
    fun getOrderedTableList(tableUUID: String? = null) {
        viewModelScope.launch {
            emitUITableState(response = ApiResponse.Loading)
            try {
                val orderedTable = repository.orderedTableList()
                var orderedTableItem: OrderedTableListItem? = null
                if (orderedTable is ApiResponse.Success) {
                    orderedTable.data.sortedBy { it.id }.let { list ->
                        val tmp = list.toMutableList()
                        tmp.add(
                            0,
                            OrderedTableListItem(
                                name = KIOSK,
                                id = KIOSK,
                                location = KIOSK,
                                uuid = KIOSK
                            )
                        )
                        _tableResponse.value = tmp
                        filteredTableResponse.value = tmp
                        orderedTableItem = tmp.firstOrNull { it.uuid == tableUUID }
                    }

                }
                emitUITableState(response = orderedTable, orderedTableItem = orderedTableItem)
            } catch (e: Exception) {
                emitUITableState(response = ApiResponse.Error(e.message))
            }

        }

    }

    /**
     * 取消订单
     */
    fun cancelOrder(orderId: String, cancelReason: String?, autoInStock: Boolean) {
        viewModelScope.launch(Dispatchers.IO) {
            withContext(Dispatchers.Main) {
                emitUIInfoRequestState(ApiResponse.Loading)
            }

            try {
                val response = repository.cancelOrder(orderId, cancelReason, autoInStock)

                if (response is ApiResponse.Success) {
                    if (ShoppingHelper.get(
                            currentOrderedInfo?.diningStyle ?: 0
                        )?.orderMoreID == orderId
                    ) {
                        ShoppingHelper.clearNote(currentOrderedInfo?.diningStyle ?: 0)
                        ShoppingHelper.clearCoupon(currentOrderedInfo?.diningStyle ?: 0)
                        ShoppingHelper.delAndCustomerAndTable(
                            currentOrderedInfo?.diningStyle ?: 0
                        )
                    }

                    getOrderedInfo(orderId)
                    withContext(Dispatchers.Main) {
                        emitUIInfoRequestState(response)
                        EventBus.getDefault()
                            .post(SimpleEvent(SimpleEventType.GET_UNREAD_EVENT, null))
                        EventBus.getDefault()
                            .post(SimpleEvent(SimpleEventType.GET_ACCEPT_ORDER_UNREAD_EVENT, null))
                    }

                } else if (response is ApiResponse.Error) {
                    withContext(Dispatchers.Main) {
                        emitUIInfoRequestState(
                            ApiResponse.Error(
                                response.message,
                                errorCode = response.errorCode
                            )
                        )
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    emitUIInfoRequestState(ApiResponse.Error(e.message))
                }
            }
        }
    }

    /**
     * 取消挂账
     */
    fun cancelCredit(orderId: String, cancelReason: String?, autoInStock: Boolean) {
        viewModelScope.launch(Dispatchers.IO) {
            withContext(Dispatchers.Main) {
                emitUIInfoRequestState(ApiResponse.Loading)
            }
            try {
                val response = repository.cancelCredit(orderId, cancelReason, autoInStock)

                if (response is ApiResponse.Success) {
                    if (ShoppingHelper.get(
                            currentOrderedInfo?.diningStyle ?: 0
                        )?.orderMoreID == orderId
                    ) {
                        ShoppingHelper.clearNote(currentOrderedInfo?.diningStyle ?: 0)
                        ShoppingHelper.clearCoupon(currentOrderedInfo?.diningStyle ?: 0)
                        ShoppingHelper.delAndCustomerAndTable(
                            currentOrderedInfo?.diningStyle ?: 0
                        )
                    }

                    getOrderedInfo(orderId)
                    withContext(Dispatchers.Main) {
                        emitUIInfoRequestState(response)
                        EventBus.getDefault()
                            .post(SimpleEvent(SimpleEventType.GET_UNREAD_EVENT, null))
                        EventBus.getDefault()
                            .post(SimpleEvent(SimpleEventType.GET_ACCEPT_ORDER_UNREAD_EVENT, null))
                    }

                } else if (response is ApiResponse.Error) {
                    withContext(Dispatchers.Main) {
                        emitUIInfoRequestState(
                            ApiResponse.Error(
                                response.message,
                                errorCode = response.errorCode
                            )
                        )
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    emitUIInfoRequestState(ApiResponse.Error(e.message))
                }
            }
        }
    }

    /**
     * 退菜
     */
    fun changeGood(
        orderNo: String,
        orderedInfoResponse: OrderedInfoResponse,
        autoInStock: Boolean
    ) {
        viewModelScope.launch {
            emitUIInfoRequestState(ApiResponse.Loading)
            try {

                var deleteGoodsNumRequest =
                    DeleteGoodsNumRequest(
                        orderNo = orderNo,
                        removeGoods = mutableListOf(),
                        autoInStock = autoInStock
                    )

                var isClearOver = false
                var residueNum = 0

                Timber.e("residueNum  ${residueNum}")

                orderedInfoResponse.goods?.forEach {
                    Timber.e("num:${it.num ?: 0}   refundsNum:${it.refundsNum ?: 0}")
                    if ((it.refundsNum ?: 0) > 0) {
                        val itemResidueNum = (it.num ?: 0) - (it.refundsNum ?: 0)
                        deleteGoodsNumRequest.removeGoods?.add(
                            RemoveGoodsBo(
                                num = itemResidueNum,
                                hashKey = it.getHash()
                            )
                        )
                        residueNum += itemResidueNum
                    } else {
                        residueNum += it.num ?: 0
                    }
                }
                if (residueNum == 0) {
                    isClearOver = true
                }

                if (isClearOver) {
                    //如果退菜退完了，直接调用取消订单
                    cancelOrder(orderNo, "", autoInStock)
                } else {
                    val changeGoodResponse = repository.changeGood(deleteGoodsNumRequest)
                    if (changeGoodResponse is ApiResponse.Success) {
                        if (changeGoodResponse.data.size == 0) {
                            getOrderedList(true)
                        } else {
                            requestOrderedInfo(orderNo)
                        }
                        emitUIInfoRequestState(ApiResponse.Success(changeGoodResponse))
                    } else if (changeGoodResponse is ApiResponse.Error) {
                        if (changeGoodResponse.errorCode != GOODS_HAS_BEEN_MERGE_OR_SPLIT) {
                            getOrderedInfo(orderNo)
                        }
                        emitUIInfoRequestState(
                            ApiResponse.Error(
                                changeGoodResponse.message,
                                errorCode = changeGoodResponse.errorCode
                            )
                        )
                    }

                }
            } catch (e: Exception) {
                Timber.e("${e.message}")
                emitUIInfoRequestState(ApiResponse.Error(e.message))
            }
        }
    }

    /**
     * 订单换桌
     */
    fun orderToChangeTable(
        allInUnpaid: Boolean,
        newTable: TableResponseItem?,
        orderNo: String?,
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val response = repository.orderToChangeTable(
                    allInUnpaid = allInUnpaid,
                    newTableUuid = newTable?.uuid,
                    orderNo = orderNo
                )
                if (response is ApiResponse.Success) {
                    Timber.e("=======orderToChangeTable")
//                    getOrderedList(true)
                    val oldTableUUid = currentOrderedInfo?.tableUuid
                    getOrderedInfo(orderNo!!)
                    //TODO 这边需要把列表里面同桌子的订单改过去,准确一点是批量查询，现在先手动换过去
                    withContext(Dispatchers.Main) {
                        emitUiChangeTableByOrder(
                            null,
                            if (allInUnpaid) newTable else null,
                            true,
                            oldTableUUid = oldTableUUid
                        )
                    }
                } else {
                    withContext(Dispatchers.Main) {
                        emitUiChangeTableByOrder(null, null, true)
                    }
                }

            } catch (e: Exception) {


            }
        }
    }

    /**
     * 确认金额(称重/修改时价菜金额)
     */
    fun confirmPendingGoods(
        context: Context,
        orderNo: String,
        orderGoods: OrderedGoods?,
        weight: String? = null,
        sellPrice: String? = null,
        vipPrice: String? = null,
        type: Int? = null,
        mealSetWeighingGoodsKey: String? = null
    ) {
        viewModelScope.launch {
            emitUIInfoRequestState(ApiResponse.Loading)
            try {
                val confirmPendingGoodsRequest =
                    ConfirmPendingGoodsRequest(
                        orderNo = orderNo,
                        weight = weight,
                        sellPrice = sellPrice,
                        vipPrice = vipPrice,
                        hashKey = orderGoods?.getHash(),
                        type = type,
                        mealSetWeighingGoodsKey = mealSetWeighingGoodsKey
                    )

                val response =
                    repository.confirmPendingGoods(confirmPendingGoodsRequest)
                if (response is ApiResponse.Success) {
                    if (response.data.isNotEmpty()) {
                        //用hash 查对应的菜品

                        val weightGoods =
                            response.data.firstOrNull { it.getHash() == orderGoods?.getHash() }
                        Timber.e("type :${type}  $weightGoods")
                        if (type == 1) {
                            Timber.e("修改价格: ${orderGoods?.isHasProcessed()}")
                            /**
                             * 第一次时价菜设置价格 且定价了才打印
                             */
                            if (orderGoods?.isHasCompletePricing() == true) {
                                requestOrderedInfo(
                                    orderNo,
                                    context = context,
                                )
                            } else {
                                if (orderGoods?.isToBeWeighed() == true && orderGoods.isHasCompleteWeight() == false) {
                                    // 称重商品 且为称重
                                    requestOrderedInfo(
                                        orderNo,
                                        context = context,
                                    )
                                } else {
                                    requestOrderedInfo(
                                        orderNo,
                                        context = context,
                                        weightGoods = weightGoods,
                                        isNeedPrint = true
                                    )
                                }

                            }
                        } else if (type == 2) {
                            //称重
                            val isPrint = if (orderGoods?.isMealSet() == true) {
                                //套餐子商品是否都完成称重
                                orderGoods.isMealSetHasCompleteWeight()
                            } else {
                                //单品直接打印
                                true
                            }
                            if (isPrint) {
                                requestOrderedInfo(
                                    orderNo,
                                    context = context,
                                    weightGoods = weightGoods,
                                    isNeedPrint = true
                                )
                            }
                        }
                    }

                    emitUIInfoRequestState(ApiResponse.Success(response))
                } else if (response is ApiResponse.Error) {
                    if (response.errorCode != GOODS_HAS_BEEN_MERGE_OR_SPLIT) {
                        getOrderedInfo(orderNo)
                    }
                    emitUIInfoRequestState(
                        ApiResponse.Error(
                            response.message,
                            errorCode = response.errorCode
                        )
                    )
                }
            } catch (e: Exception) {
                Timber.e("${e.message}")
                emitUIInfoRequestState(ApiResponse.Error(e.message))
            }
        }
    }

    /**
     * 提交订单
     */
    fun submitOrder(context: Context) {
        val connectUSD = Printer.isPosPrinterConnectUSB()
        connectUSD.addListener(object : ListenableFuture.Listener<Boolean> {
            override fun onSuccess(result: Boolean) {
                viewModelScope.launch {
                    if (_uiInfoRequestState.value?.baseBooleanResponse is ApiResponse.Loading) {
                        return@launch
                    }
                    emitUIInfoRequestState(ApiResponse.Loading)
                    val goodsVoList = currentOrderedInfo?.goods
                    val goodsBoList = OrderHelper.getGoodsBoList(goodsVoList)
//                    val goodsBoList = ArrayList<GoodsBo>()
//                    goodsVoList?.forEach {
//                        val bo = GoodsBo(
//                            id = it.id,
//                            num = it.num,
//                            tagItemId = it.getTagItemId(),
//                            feeds = it.getFeedBo(),
//                            pricingMethod = it.pricingMethod,
//                            weight = it.weight,
//                            discountPrice = it.discountPrice,
//                            goodsType = it.goodsType,
//                            mealSetGoodsDTOList = OrderHelper.getSelectMealSetGood(it.orderMealSetGoodsDTOList),
//                            singleItemDiscount = it.singleItemDiscount?.coverToSingleDiscountRequest(),
//                            goodsHashKey = it.getHash(),
//                            note = it.note
//                        )
//                        goodsBoList.add(
//                            bo
//                        )
//                    }
                    try {
                        val orderNo = currentOrderedInfo?.orderNo
                        val paymentRequest = PaymentRequest(
                            diningStyle = currentOrderedInfo?.diningStyle,
                            tableUuid = currentOrderedInfo?.tableUuid,
                            customerInfoVo = currentOrderedInfo?.customerInfoVo,
                            goodsList = goodsBoList,
                            isPreOrder = currentOrderedInfo?.isReservation,
                            orderNo = orderNo,
                            isPosPrint = result,
                            note = currentOrderedInfo?.note ?: "",
                            couponCode = currentOrderedInfo?.getCurrentCoupon()?.code,
                        )

                        val response = repository.cartCreateOrder(
                            paymentRequest, PayTypeEnum.PAY_AFTER.id
                        )

                        if (response is ApiResponse.Success) {
                            requestOrderedInfo(orderNo, context, isNeedPrint = true)

//                            if (orderRecord != null && orderRecord?.orderNo == orderNo) {
//                                getOrderedInfo(
//                                    orderRecord!!,
//                                    context,
//                                    orderNo,
//                                    isConfirmAction = true
//                                )
//                            }
                            emitUIInfoRequestState(ApiResponse.Success(response))
                        } else if (response is ApiResponse.Error) {

                            if (orderNo != null) {
                                getOrderedInfo(orderNo)
                            }
                            emitUIInfoRequestState(ApiResponse.Error(response.message))
                        }


                    } catch (e: Exception) {
                        emitUIInfoRequestState(ApiResponse.Error(e.message))
                    }
                }
            }

            override fun onFailure(e: ExecutionException) {
            }

        })
    }


    fun uncompletedOrderCount(
        srcTableUuid: String?,
        targetTable: TableResponseItem?,
        orderNo: String?
    ) {
        emitUiChangeTableByOrder(ApiResponse.Loading)
        viewModelScope.launch {
            try {
                val apiResponse =
                    repository.uncompletedOrderCount(srcTableUuid, targetTable?.uuid)
                if (apiResponse is ApiResponse.Success) {
                    Timber.e("hasUncompletedOrder srcResponse： ${apiResponse.data}")
                    emitUiChangeTableByOrder(apiResponse, targetTable)
                } else if (apiResponse is ApiResponse.Error) {
                    emitUiChangeTableByOrder(ApiResponse.Error(apiResponse.message))
                }

            } catch (e: Exception) {
                emitUiChangeTableByOrder(ApiResponse.Error(e.message))
            }

//            try {
//                val apiResponse = repository.uncompletedOrderCountV2(
//                    srcTableUuid,
//                    targetTable?.uuid,
//                    orderNo
//                )
//                if (apiResponse is ApiResponse.Success) {
//                    Timber.e("hasUncompletedOrder srcResponse： ${apiResponse.data}")
//                    emitUiChangeTableByOrder(apiResponse, targetTable)
//                } else if (apiResponse is ApiResponse.Error) {
//                    emitUiChangeTableByOrder(ApiResponse.Error(apiResponse.message))
//                }
//
//            } catch (e: Exception) {
//                emitUiChangeTableByOrder(ApiResponse.Error(e.message))
//            }
        }
    }

    private suspend fun emitUIRequestState(
        paymentResponse: ApiResponse<PaymentResponse>? = null,
        refundResponse: ApiResponse<PaymentResponse>? = null,
    ) {
        withContext(Dispatchers.Main) {
            _uiRequestState.value = UIRequestState(paymentResponse, refundResponse)
        }
    }

    data class UIRequestState(
        val paymentResponse: ApiResponse<PaymentResponse>?,
        val refundResponse: ApiResponse<PaymentResponse>?,
    )

    private suspend fun emitUIInfoState(
        orderedInfoResponse: ApiResponse<OrderedInfoResponse>? = null,
        record: OrderedRecord? = null
    ) {
        val uiModel = UIInfoModel(orderedInfoResponse, record)
        withContext(Dispatchers.Main) {
            _uiInfoState.value = uiModel
        }
    }

    data class UIInfoModel(
        val orderedInfoResponse: ApiResponse<OrderedInfoResponse>?,
        val record: OrderedRecord?
    )

    private suspend fun emitUIListState(
        showLoading: Boolean? = null,
        showError: String? = null,
        showSuccess: OrderedTotalResponse? = null,
        showEnd: Boolean = false,
        isRefresh: Boolean? = null,
        confirmOrderNo: String? = null,
        currentOrderMore: ArrayList<GoodsBo>? = null,
        localOrderNo: String? = null, //定位到的位置
        isNeedPrint: Boolean? = false, //是否需要打印
        finishPay: Boolean? = false, //是否支付完成
    ) {
        val uiModel = UIListModel(
            showLoading,
            showError,
            showSuccess,
            showEnd,
            isRefresh,
            confirmOrderNo,
            currentOrderMore,
            localOrderNo,
            isNeedPrint,
            finishPay
        )
        withContext(Dispatchers.Main) {
            _uiListState.value = uiModel
        }
    }


    data class UIListModel(
        val showLoading: Boolean?,
        val showError: String?,
        val showSuccess: OrderedTotalResponse?,
        val showEnd: Boolean,
        val isRefresh: Boolean?,
        val confirmOrderNo: String?,
        val currentOrderMore: ArrayList<GoodsBo>?,
        val localOrderNo: String?,
        val isNeedPrint: Boolean? = false, //是否需要打印
        val finishPay: Boolean? = false,
    )

    fun updateShoppingRecord(
        it: ReserveTableRequest,
        diningStyleEnum: Int,
        goods: List<OrderedGoods>
    ) {
        viewModelScope.launch {
            emitUiSaveRecordState(showLoading = true)
            ShoppingHelper.clearNote(diningStyleEnum)
            ShoppingHelper.clearCoupon(diningStyleEnum)
            ShoppingHelper.delAndCustomerAndTable(diningStyleEnum)
            ShoppingHelper.updateSelectTable(
                currentOrderedInfo?.tableUuid,
                currentOrderedInfo?.tableName,
                currentOrderedInfo?.tableType,
                diningStyleEnum
            )
            ShoppingHelper.updateCustomer(diningStyleEnum, it)
            var shoppingRecord =
                ShoppingHelper.updateOrderMore(
                    true,
                    currentOrderedInfo?.orderNo,
                    diningStyleEnum
                )

            if (currentOrderedInfo?.payStatus == OrderedStatusEnum.TO_BE_CONFIRM.id) {
                shoppingRecord =
                    ShoppingHelper.updatePayStatus(
                        currentOrderedInfo?.payStatus,
                        diningStyleEnum
                    )
            }


            withContext(Dispatchers.Main) {
                emitUiSaveRecordState(showSuccess = true, shoppingRecord = shoppingRecord)
            }
        }
    }


    private suspend fun emitUiSaveRecordState(
        showLoading: Boolean? = null,
        showSuccess: Boolean? = null,
        shoppingRecord: ShoppingRecord? = null
    ) {
        val uiModel =
            UISaveRecordStateModel(showLoading, showSuccess, shoppingRecord)
        withContext(Dispatchers.Main) {
            _uiSaveRecordState.value = uiModel
        }
    }

    private suspend fun emitUITableState(
        response: ApiResponse<OrderedTableListResponse>? = null,
        orderedTableItem: OrderedTableListItem? = null
    ) {
        val uiModel = UITableStateModel(response, orderedTableItem)
        withContext(Dispatchers.Main) {
            _uiTableState.value = uiModel
        }
    }

    data class UITableStateModel(
        val response: ApiResponse<OrderedTableListResponse>?,
        val orderedTableItem: OrderedTableListItem?
    )

    data class UISaveRecordStateModel(
        val showLoading: Boolean?,
        val showSuccess: Boolean?,
        val shoppingRecord: ShoppingRecord?
    )


    private suspend fun emitUIInfoRequestState(
        baseBooleanResponse: ApiResponse<Any>? = null,
    ) {
        val uiInfoRequestModel = UIInfoRequestModel(baseBooleanResponse)
        withContext(Dispatchers.Main) {
            _uiInfoRequestState.value = uiInfoRequestModel
        }
    }

    data class UIInfoRequestModel(
        val baseBooleanResponse: ApiResponse<Any>?,
    )


    private fun emitUiChangeTableByOrder(
        srcApiResponse: ApiResponse<List<TableOrderStatus>>? = null,
        newTable: TableResponseItem? = null,
        isOver: Boolean? = false,
        oldTableUUid: String? = null
    ) {

        val uiModel =
            UIChangeTableByOrder(
                apiResponse = srcApiResponse,
                newTable = newTable,
                isOver = isOver,
                oldTableUUid = oldTableUUid
            )

        _uiChangeTableByOrderState.postValue(uiModel)
    }

    data class UIChangeTableByOrder(
        var apiResponse: ApiResponse<List<TableOrderStatus>>? = null,
        var newTable: TableResponseItem? = null,
        var isOver: Boolean? = false,
        var oldTableUUid: String? = null
    )


    fun filterFloor(query: String) {
        if (query.isNotEmpty()) {
            val filteredList = _tableResponse.value?.filter { table ->
                table.location?.equals(query, ignoreCase = true) == true
            }
            Timber.e("recyclerViewPage  filteredList  ${filteredList?.size}  $query")
            val orderedTableListResponse = OrderedTableListResponse()
            filteredList?.let { orderedTableListResponse.addAll(it) }

            _filteredTableResponse.value = orderedTableListResponse
        } else {
            _filteredTableResponse.value = _tableResponse.value
        }
    }


    fun exportAgain(
        context: Context,
        cashierCheck: Boolean,  //是否是结账小票
        isPreSettlement: Boolean, //是否预结算小票
        printerViewModel: PrinterConfirmViewModel,
        callback: (File?) -> Unit,
    ) {

        val isHasNeedProcess = currentOrderedInfo?.isHasNeedProcess()

        if (isPreSettlement) {
            if (isHasNeedProcess == false) {
                printerViewModel.preSettlement(currentOrderedInfo, false)
            } else {
                Toast.makeText(
                    context,
                    context.getString(R.string.order_has_no_price_good),
                    Toast.LENGTH_SHORT
                ).show()
                callback?.invoke(null)
                return
            }
        }

        if (cashierCheck) {
            exportPrinterTemplate(
                context,
                printTicketType = PrintTicketType.NORMAL.id,
                isPreSettlement = false,
                isPrinterAgain = false,
                callback = callback
            )
        }

        if (isPreSettlement) {
            if (isHasNeedProcess == false) {
                exportPrinterTemplate(
                    context,
                    printTicketType = PrintTicketType.NONE.id,
                    isPreSettlement = true,
                    isPrinterAgain = false,
                    callback = callback
                )
            }
        }
    }


    fun exportPrinterTemplate(
        context: Context,
        printTicketType: Int = PrintTicketType.NONE.id,
        isPrinterAgain: Boolean = false,
        isPreSettlement: Boolean? = false, //是否预结算小票
        callback: (File?) -> Unit,
    ) {
        viewModelScope.launch {
            if (currentOrderedInfo != null) {
                exportTemplate(
                    context,
                    currentOrderedInfo!!,
                    printTicketType,
                    isPrinterAgain,
                    isPreSettlement,
                    callback
                )
            }
        }
    }


    private suspend fun exportTemplate(
        context: Context,
        currentOrderedInfo: OrderedInfoResponse,
        printTicketType: Int = PrintTicketType.NONE.id,
        isPrinterAgain: Boolean = false,
        isPreSettlement: Boolean? = false, //是否预结算小票
        callback: (File?) -> Unit,
    ) {

        try {
            Timber.e("currentOrderedInfo.payStatus : ${currentOrderedInfo.payStatus}")
            val printTemplateResponse = repository.getPrintTemplate()
            if (printTemplateResponse is ApiResponse.Success) {
                val printTemplateList = printTemplateResponse.data

                //拷贝一个对象 用来打印用，对后续有对对象操作不要影响原对象
                val copyCurrentOrderedInfo = currentOrderedInfo.clone()
                if (!currentOrderedInfo.currentOrderMoreList.isNullOrEmpty()) {
                    copyCurrentOrderedInfo.currentOrderMoreList =
                        (currentOrderedInfo.currentOrderMoreList!!.clone() as ArrayList<OrderedGoods>)
                    currentOrderedInfo.currentOrderMoreList?.clear()
                }

                Timber.e("currentOrderedInfo 加购列表  ${currentOrderedInfo.currentOrderMoreList?.size}")
                Timber.e("copyCurrentOrderedInfo 加购列表  ${copyCurrentOrderedInfo.currentOrderMoreList?.size}")

                repository.orderTranslate(copyCurrentOrderedInfo)
                var qrcode: String? = null

                if (isPreSettlement == true) {
                    //预结小票翻译
                    var printTemplateResponseItem: PrintTamplateResponseItem? = null
                    val tmpPrintTemplateResponseItem =
                        printTemplateList.firstOrNull {
                            it.type == PrintTemplateTypeEnum.PRE_CHECKOUT_RECEIPT.id
                        }
                    if (tmpPrintTemplateResponseItem != null) {
                        printTemplateResponseItem = tmpPrintTemplateResponseItem
                    }
                    //如果有预结算模板
                    if (printTemplateResponseItem != null) {
                        if (printTemplateResponseItem.informationShow?.showKhqrCode == true && PaymentMethodHelper.isSupportOnline() && (copyCurrentOrderedInfo.payStatus == OrderedStatusEnum.UNPAID.id || copyCurrentOrderedInfo.payStatus == OrderedStatusEnum.BE_CONFIRM.id) && PaymentMethodHelper.isSupportOnline() && MainDashboardFragment.CURRENT_USER?.isPaymentInAdvance == false && !copyCurrentOrderedInfo.isUniversalTable() && copyCurrentOrderedInfo.isDineIn() && !copyCurrentOrderedInfo.isFromKiosk() && copyCurrentOrderedInfo.getTotalPriceBySelf() != 0L) {

                            val response = repository.getKhqr(
                                PreSettlementRequest(
                                    orderNo = copyCurrentOrderedInfo?.orderNo,
                                    couponCode = copyCurrentOrderedInfo?.getCurrentCoupon()?.code,
                                    reduceType = copyCurrentOrderedInfo?.getWholeDiscountType(),
                                    reduceRate = copyCurrentOrderedInfo?.wholeDiscountReduce?.reduceRate,
                                    reduceDollar = copyCurrentOrderedInfo?.wholeDiscountReduce?.reduceDollar,
                                    reduceVipDollar = copyCurrentOrderedInfo?.wholeDiscountReduce?.reduceDollar,
                                    discountReduceActivityId = copyCurrentOrderedInfo?.discountReduceActivity?.id
                                )
                            )
                            if (response is ApiResponse.Success) {
                                qrcode = response.data.qrcode
                            }
                        }
                    }
                }

                var ticketBitmap: Bitmap? = null

                if (printTemplateList.isNotEmpty()) {
                    //获取菜品的多语言
                    Timber.e("开始打印 printTicketType:${printTicketType}  isPreSettlement:${isPreSettlement}")
                    val printType = currentOrderedInfo.getPrintTypeFromDiningStyle()

                    val printTemplate =
                        printTemplateList.firstOrNull { it.type == printType.id }

                    val preCheckOutTemplate =
                        printTemplateList.firstOrNull { it.type == PrintTemplateTypeEnum.PRE_CHECKOUT_RECEIPT.id }

                    Timber.e("打印模板 id  ${printTemplate?.type}   ${printType.id}")
                    Timber.e("预打印模板 id  ${preCheckOutTemplate?.type} ")

                    if (printTemplate != null && printTicketType != PrintTicketType.NONE.id && currentOrderedInfo.isOrderSuccess()) {
                        //临时记录一下打印的 已支付订单号 防止ws 收到重复打印
                        PrinterDeviceHelper.setOrderPrinter(currentOrderedInfo.orderNo)
                    }

                    if (printTicketType == PrintTicketType.NORMAL.id) {
                        if (printTemplate != null) {
                            Timber.e(" 打印")
                            ticketBitmap = Printer.exportTicket(
                                context,
                                printTemplate,
                                copyCurrentOrderedInfo,
                                isPrinterAgain
                            )
                        }
                    }

                    if (isPreSettlement == true) {
                        if (preCheckOutTemplate != null) {
                            Timber.e(" 打印预结算小票   ${preCheckOutTemplate.informationShow?.showKhqrCode}")
                            Timber.e(" qrcode   $qrcode")
                            ticketBitmap = Printer.exportTicket(
                                context,
                                preCheckOutTemplate,
                                copyCurrentOrderedInfo,
                                isPrinterAgain,
                                paymentQrCode = qrcode
                            )
                        }
                    }
                }

                if (ticketBitmap != null) {
                    //导出小票
                    val marginH = 20
                    val marginV = 50
                    val document = PdfDocument()
                    //调整Bitmap尺寸适配PDF页面 以A4纸（ISO标准）为例，计算缩放比例：
                    val pageWidth =
                        PrintAttributes.MediaSize.ISO_A4.widthMils * 72 / 1000 // 转换为像素
                    val scale = (pageWidth - 2 * marginH).toFloat() / (ticketBitmap.width)
                    val pageHeight = (ticketBitmap.height * scale).toInt() + 2 * marginV
                    //绘制Bitmap到PDF页面
                    val pageInfo =
                        PdfDocument.PageInfo.Builder(pageWidth, pageHeight, 1).create()
                    val page = document.startPage(pageInfo)
                    val canvas = page.canvas
                    canvas.translate(marginH.toFloat(), marginV.toFloat())
                    val matrix = Matrix().apply { postScale(scale, scale) }
                    canvas.drawBitmap(ticketBitmap, matrix, Paint(Paint.ANTI_ALIAS_FLAG))
                    document.finishPage(page)
                    //保存PDF文件
                    val dir = FolderHelper.getDownloadFolderPath()
                    val pdfFile = File(
                        dir, "${
                            Date().formatDateStr(FORMAT_DATE_EXPORT_NAME)
                        }-${currentOrderedInfo.orderNo}.pdf"
                    )
                    if (pdfFile.exists()) {
                        pdfFile.delete()
                    }
                    val outputStream = FileOutputStream(pdfFile)
                    document.writeTo(outputStream)
                    document.close()
                    outputStream.close()
                    Timber.d("save pdf success:${pdfFile}")
                    callback(pdfFile)
                }
            }
        } catch (e: Exception) {
            callback(null)
            Timber.d(e.message)
        }
    }


    fun printerAgain(
        context: Context,
        cashierCheck: Boolean,  //堂食
        kitchenCheckType: Int, //厨房
        isPreSettlement: Boolean, //是否预结算小票
        isPrintLabel: Boolean,//是否打印标签
        printerViewModel: PrinterConfirmViewModel
    ) {

        val isHasNeedProcess = currentOrderedInfo?.isHasNeedProcess()

        if (isPreSettlement) {
            if (isHasNeedProcess == false) {
                printerViewModel.preSettlement(currentOrderedInfo)
            } else {
                Toast.makeText(
                    context,
                    context.getString(R.string.order_has_no_price_good),
                    Toast.LENGTH_SHORT
                ).show()
            }
        }

        if (cashierCheck || kitchenCheckType != KitchenCheckTicketType.NONE.id) {
            val requestObject = PrinterAgainRequest(
                cashierCheck,
                kitchenCheckType != KitchenCheckTicketType.NONE.id,
                currentOrderedInfo?.id,
                if (kitchenCheckType != KitchenCheckTicketType.NONE.id) kitchenCheckType else null
            )
            printerViewModel.postPrinterAgain(requestObject)
        }


        if (cashierCheck) {
            getPrinterTemplate(
                context,
                printTicketType = PrintTicketType.NORMAL.id,
                isPrinterAgain = true
            )
        }

        if (kitchenCheckType != KitchenCheckTicketType.NONE.id) {
            getPrinterTemplate(
                context,
                kitchenCheckType,
                isPrinterAgain = true
            )
        }
        if (isPreSettlement) {
            if (isHasNeedProcess == false) {
                getPrinterTemplate(
                    context,
                    isPreSettlement = true,
                    isPrinterAgain = false
                )
            }
        }

        if (isPrintLabel) {
            getPrinterTemplate(
                context,
                isPrintLabel = true,
                isPrinterAgain = false
            )
        }

    }

    fun getPrinterTemplate(
        context: Context,
        kitchenCheckType: Int = KitchenCheckTicketType.NONE.id,
        printTicketType: Int = PrintTicketType.NONE.id,
        isPrinterAgain: Boolean = false,
        isPreSettlement: Boolean? = false, //是否预结算小票
        isPrintLabel: Boolean? = false, //是否打印标签
    ) {
        viewModelScope.launch {
            if (currentOrderedInfo != null) {
                /**
                 * 手动补打 最新加购
                 */
                if (kitchenCheckType == KitchenCheckTicketType.PART.id) {
                    //查找最后主
                    currentOrderedInfo?.currentOrderMoreList =
                        OrderHelper.getLastAddGoods(currentOrderedInfo)
                }

                printerTemplate(
                    context,
                    currentOrderedInfo!!,
                    kitchenCheckType != KitchenCheckTicketType.NONE.id,
                    printTicketType,
                    isPrinterAgain,
                    isPreSettlement,
                    isPrintLabel
                )
            }
        }
    }

    private suspend fun printerTemplate(
        context: Context,
        currentOrderedInfo: OrderedInfoResponse,
        isKitchen: Boolean? = false,
        printTicketType: Int = PrintTicketType.NONE.id,
        isPrinterAgain: Boolean = false,
        isPreSettlement: Boolean? = false, //是否预结算小票
        isPrintLabel: Boolean? = false
    ) {

        try {
            Timber.e("currentOrderedInfo.payStatus : ${currentOrderedInfo.payStatus}")

            val printInfoList = PrinterDeviceHelper.getPrinterList()
            //拷贝一个对象 用来打印用，对后续有对对象操作不要影响原对象
            val copyCurrentOrderedInfo = currentOrderedInfo.clone()
            if (!currentOrderedInfo.currentOrderMoreList.isNullOrEmpty()) {
                copyCurrentOrderedInfo.currentOrderMoreList =
                    (currentOrderedInfo.currentOrderMoreList!!.clone() as ArrayList<OrderedGoods>)
                currentOrderedInfo.currentOrderMoreList?.clear()
            }

            Timber.e("currentOrderedInfo 加购列表  ${currentOrderedInfo.currentOrderMoreList?.size}")
            Timber.e("copyCurrentOrderedInfo 加购列表  ${copyCurrentOrderedInfo.currentOrderMoreList?.size}")

            repository.orderTranslate(copyCurrentOrderedInfo)
            var qrcode: String? = null

            if (isPreSettlement == true) {
                //预结小票翻译
                var printTemplateResponseItem: PrintTamplateResponseItem? = null
                for (printerConfigInfo in printInfoList) {
                    val printTemplateList =
                        printerConfigInfo.printerTemplateList ?: listOf()
                    val tmpPrintTemplateResponseItem =
                        printTemplateList.firstOrNull {
                            it.type == PrintTemplateTypeEnum.PRE_CHECKOUT_RECEIPT.id
                        }
                    if (tmpPrintTemplateResponseItem != null) {
                        printTemplateResponseItem = tmpPrintTemplateResponseItem
                    }
                }
                //如果有预结算模板
                if (printTemplateResponseItem != null) {
                    if (printTemplateResponseItem.informationShow?.showKhqrCode == true && PaymentMethodHelper.isSupportOnline() && (copyCurrentOrderedInfo.payStatus == OrderedStatusEnum.UNPAID.id || copyCurrentOrderedInfo.payStatus == OrderedStatusEnum.BE_CONFIRM.id) && PaymentMethodHelper.isSupportOnline() && MainDashboardFragment.CURRENT_USER?.isPaymentInAdvance == false && !copyCurrentOrderedInfo.isUniversalTable() && copyCurrentOrderedInfo.isDineIn() && !copyCurrentOrderedInfo.isFromKiosk() && copyCurrentOrderedInfo.getTotalPriceBySelf() != 0L) {
                        //只请求一次预付款二维码
                        val response = repository.getKhqr(
                            PreSettlementRequest(
                                orderNo = copyCurrentOrderedInfo?.orderNo,
                                couponCode = copyCurrentOrderedInfo?.getCurrentCoupon()?.code,
                                reduceType = copyCurrentOrderedInfo?.getWholeDiscountType(),
                                reduceRate = copyCurrentOrderedInfo?.wholeDiscountReduce?.reduceRate,
                                reduceDollar = copyCurrentOrderedInfo?.wholeDiscountReduce?.reduceDollar,
                                reduceVipDollar = copyCurrentOrderedInfo?.wholeDiscountReduce?.reduceDollar,
                                discountReduceActivityId = copyCurrentOrderedInfo?.discountReduceActivity?.id
                            )
                        )
                        if (response is ApiResponse.Success) {
                            qrcode = response.data.qrcode
                        }
                    }
                }
            }


            var isPrinter = false
            for (printerConfigInfo in printInfoList) {

                val printTemplateList =
                    printerConfigInfo.printerTemplateList ?: listOf()
                if (printTemplateList.isNotEmpty()) {
                    //获取菜品的多语言
                    Timber.e("开始打印  isKitchen:${isKitchen}  printTicketType:${printTicketType}  isPreSettlement:${isPreSettlement}")
                    val kitchenPrintTemplate =
                        printTemplateList.filter { it.type == PrintTemplateTypeEnum.KITCHEN.id }
                    val printType =
                        currentOrderedInfo.getPrintTypeFromDiningStyle()

                    val printTemplate =
                        printTemplateList.firstOrNull { it.type == printType?.id }

                    val preCheckOutTemplate =
                        printTemplateList.firstOrNull { it.type == PrintTemplateTypeEnum.PRE_CHECKOUT_RECEIPT.id }

                    val labelTickerTemplate =
                        printTemplateList.firstOrNull { it.type == PrintTemplateTypeEnum.LABEL.id }


                    Timber.e("打印模板 id  ${printTemplate?.type}   ${printType?.id}")
                    Timber.e("预打印模板 id  ${preCheckOutTemplate?.type} ")

                    if (printTemplate != null && printTicketType != PrintTicketType.NONE.id && currentOrderedInfo.isOrderSuccess()) {
                        //临时记录一下打印的 已支付订单号 防止ws 收到重复打印
                        PrinterDeviceHelper.setOrderPrinter(currentOrderedInfo.orderNo)
                    }
                    if (isKitchen == true) {
                        //厨打小票
                        Timber.e("Chetwyn 厨打小票 ${printTemplate}")
                        if (kitchenPrintTemplate.isNotEmpty()) {
                            kitchenPrintTemplate.forEach {
                                Printer.printTicket(
                                    context,
                                    it,
                                    copyCurrentOrderedInfo,
                                    isPrinterAgain,
                                    printerConfigInfo = printerConfigInfo,
                                )
                            }
                            isPrinter = true
                        }
                    }

                    if (printTicketType == PrintTicketType.NORMAL.id) {
                        if (printTemplate != null) {
                            Timber.e("Chetwyn 打印2002 ${printTemplate}")
                            Printer.printTicket(
                                context,
                                printTemplate,
                                copyCurrentOrderedInfo,
                                isPrinterAgain,
                                printerConfigInfo = printerConfigInfo,
                            )

                            isPrinter = true
                        }
                    }

                    if (isPreSettlement == true) {
                        if (preCheckOutTemplate != null) {
                            Timber.e("Chetwyn 打印预结算小票   ${preCheckOutTemplate.informationShow?.showKhqrCode}")
                            Timber.e(" qrcode   $qrcode")
                            Printer.printTicket(
                                context,
                                preCheckOutTemplate,
                                copyCurrentOrderedInfo,
                                isPrinterAgain,
                                paymentQrCode = qrcode,
                                printerConfigInfo = printerConfigInfo,
                            )
                            isPrinter = true
                        }
                    }

                    if (isPrintLabel == true && labelTickerTemplate != null) {
                        Timber.e("Chetwyn printLabelTicket   ${preCheckOutTemplate?.informationShow?.showKhqrCode}")
                        LabelPrinter.printLabelTicket(
                            context,
                            labelTickerTemplate,
                            copyCurrentOrderedInfo,
                            printerConfigInfo
                        )
                    }
                }

//                //如果wifi打印机状态不正常直接逃过
                if (printerConfigInfo.type == PrinterTypeEnum.WIFI.type) {
                    val printerState =
                        PrinterDeviceHelper.getWifiConnectState(printerConfigInfo)
                    if (!printerState && isPrinter) {
                        isPrinter = false
                    }
                }
            }

            if (isPrinter) {
                val printResponse = repository.updatePrintLog(copyCurrentOrderedInfo.orderNo)
                if (printResponse is ApiResponse.Success) {
                    EventBus.getDefault().post(
                        SimpleEvent(
                            SimpleEventType.UPDATE_UNPRINT_EVENT,
                            currentOrderedInfo.orderNo
                        )
                    )
                    EventBus.getDefault().post(
                        SimpleEvent(
                            SimpleEventType.GET_UNREAD_EVENT,
                            null
                        )
                    )
                }
            }
        } catch (e: Exception) {
            Timber.e(e);
        }
    }


    fun updateOrderMoreScreen(orderNo: String?) {
        if (ShoppingHelper.get(0)?.orderMoreID == orderNo) {
            ShoppingHelper.delAndCustomerAndTable(0)
        }
    }

    fun updateOrderInfo(orderInfo: OrderedInfoResponse?) {
        viewModelScope.launch {
            if (orderInfo != null) {
                currentOrderedInfo = orderInfo
                emitUIInfoState(
                    orderedInfoResponse = ApiResponse.Success(currentOrderedInfo!!),
                )
            }
        }
    }

//    fun updateOrderNote(note: String?) {
//        currentOrderedInfo?.note = note ?: ""
//    }

    /**
     * 取消接单
     *
     *
     * @param cancelReason
     */
    fun cancelAcceptOrder(cancelReason: String?) {
        viewModelScope.launch {
            emitUIInfoRequestState(ApiResponse.Loading)
            try {
                val response = repository.cancelAcceptOrder(
                    currentOrderedInfo?.waitAcceptAddOrder?.id,
                    cancelReason
                )

                if (response is ApiResponse.Success) {
//                    emitUIRemoveOrderState(id)
                    emitUIInfoRequestState(response)
                    if (currentOrderedInfo?.orderNo != null) {
                        getOrderedInfo(currentOrderedInfo?.orderNo!!)
                    }
                    EventBus.getDefault()
                        .post(SimpleEvent(SimpleEventType.GET_ACCEPT_ORDER_UNREAD_EVENT, null))
//                    EventBus.getDefault().post(SimpleEvent(SimpleEventType.GET_UNREAD_EVENT, null))

                } else if (response is ApiResponse.Error) {
                    emitUIInfoRequestState(
                        ApiResponse.Error(
                            response.message,
                            errorCode = response.errorCode
                        )
                    )
                }
            } catch (e: Exception) {
                emitUIInfoRequestState(ApiResponse.Error(e.message))
            }
        }
    }


    /**
     * 接单
     *
     * @param context
     */
    fun receivingOrder(context: Context) {
        viewModelScope.launch {
            if (_uiInfoRequestState.value?.baseBooleanResponse is ApiResponse.Loading) {
                return@launch
            }
            emitUIInfoRequestState(ApiResponse.Loading)

            try {
                val orderNo = currentOrderedInfo?.orderNo

                val response = repository.acceptOrder(orderNo = currentOrderedInfo?.orderNo)

                if (response is ApiResponse.Success) {
                    emitUIInfoRequestState(ApiResponse.Success(response))
                    if (orderRecord != null && orderRecord?.orderNo == orderNo && orderNo != null) {
                        val goodsVoList = currentOrderedInfo?.waitAcceptAddOrder?.goods
                        val goodsBoList = OrderHelper.getGoodsBoList(goodsVoList)
//                        val goodsBoList = ArrayList<GoodsBo>()
//                        goodsVoList?.forEach {
//                            goodsBoList.add(
//                                GoodsBo(
//                                    id = it.id,
//                                    num = it.num,
//                                    tagItemId = it.getTagItemId(),
//                                    feeds = it.getFeedBo(),
//                                    pricingMethod = it.pricingMethod,
//                                    weight = it.weight,
//                                    discountPrice = it.discountPrice,
//                                    goodsType = it.goodsType,
//                                    mealSetGoodsDTOList = OrderHelper.getSelectMealSetGood(it.orderMealSetGoodsDTOList),
//                                    goodsHashKey = it.getHash(),
//                                    note = it.note
//                                )
//                            )
//                        }
                        getOrderedInfo(
                            orderRecord!!,
                            context,
                            currentOrderMore = goodsBoList,
                            isNeedPrint = true
                        )
                    }

                } else if (response is ApiResponse.Error) {
                    if (orderNo != null) {
                        getOrderedInfo(orderNo)
                    }
                    emitUIInfoRequestState(
                        ApiResponse.Error(
                            response.message
                        )
                    )
                }

                EventBus.getDefault()
                    .post(SimpleEvent(SimpleEventType.GET_ACCEPT_ORDER_UNREAD_EVENT, null))

            } catch (e: Exception) {
                emitUIInfoRequestState(ApiResponse.Error(e.message))
            }
        }
    }

    /**
     * 反结账
     *
     */
    fun antiSettlement(
        context: Context,
        orderInfo: OrderedInfoResponse?,
        reasonType: Int,
        reason: String?
    ) {
        viewModelScope.launch {
            try {
                emitUIInfoRequestState(ApiResponse.Loading)
                val response = repository.reverseCheckOut(
                    orderNo = orderInfo?.orderNo!!,
                    reasonType = reasonType,
                    reason = reason
                )
                if (response is ApiResponse.Success) {
                    if (orderInfo.payType == PayTypeEnum.ONLINE_PAYMENT.id) {
                        Toast.makeText(
                            context,
                            context.getString(R.string.anti_settlement_success_cue),
                            Toast.LENGTH_SHORT
                        )
                            .show()
                    }
                    emitUIInfoRequestState(response)
                    getOrderedInfo(orderInfo.orderNo)
                } else if (response is ApiResponse.Error) {
                    emitUIInfoRequestState(
                        ApiResponse.Error(
                            response.message,
                            errorCode = response.errorCode
                        )
                    )
                }
            } catch (e: Exception) {
                emitUIInfoRequestState(ApiResponse.Error(e.message))
            }
        }
    }

    /**
     * 单品折扣
     *
     * @param orderNo
     * @param singleReduceReason
     * @param singleItemDiscountList
     */
    fun setSingleReduceDiscount(
        orderNo: String,
        singleReduceReason: String? = null,
        singleItemDiscountList: List<SingleDiscountRequest>?
    ) {
        viewModelScope.launch {
            try {
                emitUIInfoRequestState(ApiResponse.Loading)
                val response = repository.setSingleItemReduceDiscount(
                    orderNo,
                    singleReduceReason,
                    singleItemDiscountList
                )
                if (response is ApiResponse.Success) {
                    getOrderedInfo(orderNo)
                    emitUIInfoRequestState(response)
                } else if (response is ApiResponse.Error) {
                    emitUIInfoRequestState(
                        ApiResponse.Error(
                            response.message,
                            errorCode = response.errorCode
                        )
                    )
                } else {
                    emitUIInfoRequestState(response)
                }

            } catch (e: Exception) {
                emitUIInfoRequestState(ApiResponse.Error(e.message))
            }
        }
    }

    /**
     * 加购临时商品到购物车
     *
     * @param goods
     */
    fun addTmpGoodToCart(context: Context, goods: List<Goods>, onSuccess: (() -> Unit)? = null) {
        viewModelScope.launch {
            try {
                val connectUSD = Printer.isPosPrinterConnectUSB()
                connectUSD.addListener(object : ListenableFuture.Listener<Boolean> {
                    override fun onSuccess(result: Boolean) {
                        viewModelScope.launch {
                            val goodsBoList = ArrayList<GoodsBo>()
                            goods?.forEach {
                                goodsBoList.add(
                                    GoodsBo(
                                        id = it?.id,
                                        num = it.totalCount,
                                        tagItemId = null,
                                        feeds = null,
                                        pricingMethod = 0,
                                        weight = null,
                                        uuid = null,
                                        discountPrice = null,
                                        goodsType = it?.goodsType,
                                        mealSetGoodsDTOList = null,
                                        singleItemDiscount = null,
                                        goodsHashKey = null,
                                        isProcessed = true,
                                        note = ""
                                    )
                                )
                            }
                            val orderNo = currentOrderedInfo?.orderNo
                            val orderMoreGoodRequest = AddOrderMoreGoodRequest(
                                orderNo = orderNo,
                                note = null,
                                isPosPrint = result,
                                goodsList = goodsBoList,
                                isValid = false
                            )
                            try {
                                val response = repository.addMoreGoods(orderMoreGoodRequest)
                                if (response is ApiResponse.Success) {
                                    onSuccess?.invoke()
                                    val list = arrayListOf<GoodsBo>()
                                    //过滤掉待称重的菜
                                    goodsBoList.filter { it.isProcessed == true }
                                        .forEach { list.add(it) }
                                    val orderedInfo = response.data
                                    orderedInfo.currentOrderMore = list
                                    orderedInfo.goods = orderedInfo.getGoodsVo().goodsList?.let {
                                        ArrayList(
                                            it
                                        )
                                    }
                                    orderedInfo.currentOrderMore?.let {
                                        val list = ArrayList<OrderedGoods>()
                                        //返回的菜单里面找出一样的菜品
                                        orderedInfo.currentOrderMore?.forEach { bo ->
                                            //可能会有同一到待称重的菜，一个没称重，一个带称重
                                            val first = orderedInfo.goods?.firstOrNull {
                                                HashHelper.getHashByTagStr(
                                                    ArrayList(bo.feeds ?: listOf()),
                                                    bo.tagItemId?.replace(",", "") ?: "",
                                                    null,
                                                    bo.id!!,
                                                    singleItemDiscount = bo.singleDiscountGoods,
                                                    goodsPriceKey = "",
                                                    note = bo.note,
                                                    uuid = bo.uuid
                                                ) == HashHelper.getHash(
                                                    ArrayList(it.feeds ?: listOf()),
                                                    ArrayList(it.tagItems ?: listOf()),
                                                    it.orderMealSetGoodsDTOList,
                                                    it.id!!,
                                                    singleItemDiscount = it.singleItemDiscount?.toCartSingleDiscountGood(),
                                                    goodsPriceKey = "",
                                                    note = it.note,
                                                    uuid = it.uuid
                                                )
                                            }?.copy()
                                            first?.let {
                                                list.add(it.apply {
//                                                    weight = null
//                                                    weighingCompleted = false
                                                    num = bo.num
                                                })
                                            }
                                        }
                                        //可能出现已经被过滤掉待称重的菜，所以此时只有currentOrderMore有值，list为空
                                        orderedInfo.currentOrderMoreList = list
                                    }
                                    //确认单加购  或者 后付款堂食待支付加购 且非待确认
                                    Timber.e("加购是否后付款 orderedInfo.isPaymentAdvance :${orderedInfo.isPaymentAdvance}")
                                    if (orderedInfo.isPaymentAdvance == false && orderedInfo.payStatus != OrderedStatusEnum.TO_BE_CONFIRM.id) {
                                        getOrderedInfo(orderNo!!)
                                        printerTemplate(
                                            context,
                                            orderedInfo,
                                            isKitchen = true,
                                            printTicketType = PrintTicketType.NORMAL.id,
                                            isPrintLabel = true
                                        )
                                    } else {
                                        getOrderedInfo(orderNo!!)
                                    }
                                }
                            } catch (e: Exception) {
                            }
                        }
                    }

                    override fun onFailure(e: ExecutionException) {
                    }

                })
            } catch (e: Exception) {

            }
        }
    }

//    /**
//     * 更新时价菜价格
//     *
//     */
//    fun updateTimeGoodPrice(
//        orderGoods: OrderedGoods?,
//        salePrice: String,
//        vipPrice: String? = null
//    ) {
//        viewModelScope.launch {
//            try {
//
//            } catch (e: Exception) {
//
//            }
//        }
//    }

    fun updateCustomerInfo(
        name: String? = null,
        areaCode: String? = null,
        mobile: String? = null,
        diningNumber: Int? = null,
        diningTime: String? = null,
        orderId: String? = null
    ) {
        viewModelScope.launch {
            try {
                val response =
                    repository.updateCustomerInfo(
                        UpdateCustomerInfoRequest(
                            name,
                            areaCode,
                            mobile,
                            diningNumber,
                            diningTime,
                            orderId
                        )
                    )
                if (response is ApiResponse.Success) {
                    if (orderId != null) {
                        getOrderedInfo(orderId)
                    }
                }

            } catch (e: Exception) {

            }
        }
    }


    fun editDeliveryOrderNo(deliveryOrderNo: String) {
        viewModelScope.launch {
            val response = repository.editDeliveryOrderNo(
                EditDeliveryOrderNoRequest(
                    currentOrderedInfo?.orderNo,
                    deliveryOrderNo,
                    currentOrderedInfo?.deliveryPlatformId
                )
            )
            if (response is ApiResponse.Success) {
                getOrderedInfo(currentOrderedInfo?.orderNo!!)
            } else if (response is ApiResponse.Error) {
                emitUIInfoRequestState(ApiResponse.Error(response.message))
            }
        }
    }

    fun updateOrderRecord(orderRecord: OrderedRecord, data: WsOrderPreSettlementChangeResponse?) {
        orderRecord.hasPrintPreSettlement = data?.hasPrintPreSettlement
        _uiSocketOrderedState.postValue(orderRecord)
    }


    private var nickNamePagNo = 1
    private val nickNamePageSize = 20
    private val _uiNickNameListState = MutableLiveData<UINickNameListModel>()
    val uiNickNameListState get() = _uiNickNameListState

    fun getConsumerList(keyword: String? = null) {
        viewModelScope.launch {
//            if (isRefresh != false) {
//                nickNamePagNo = 1
//                if (isRefresh == null) {
//                    emitUINickNameListState(showLoading = true)
//                }
//            }
            try {
                val response = repository.getConsumer(keyword ?: "")
                withContext(Dispatchers.Main) {
                    if (response is ApiResponse.Success) {
                        val response = response.data
                        if (response.isNullOrEmpty()) {
                            emitUINickNameListState(
                                showEnd = true,
//                                isRefresh = isRefresh,
                                showLoading = false
                            )
                            return@withContext
                        }
                        nickNamePagNo++
                        emitUINickNameListState(
                            showSuccess = response,
//                            isRefresh = isRefresh,
                            showLoading = false
                        )

                    } else if (response is ApiResponse.Error) {
                        emitUINickNameListState(showError = response.message, showLoading = false)
                    }
                }
            } catch (e: Exception) {
                emitUINickNameListState(showError = e.message, showLoading = false)
            }
        }
    }

    data class UINickNameListModel(
        val showLoading: Boolean?,
        val showError: String?,
        val showSuccess: List<ConsumerResponse>?,
        val showEnd: Boolean,
        val isRefresh: Boolean?,
    )

    private suspend fun emitUINickNameListState(
        showLoading: Boolean? = null,
        showError: String? = null,
        showSuccess: List<ConsumerResponse>? = null,
        showEnd: Boolean = false,
        isRefresh: Boolean? = null,
    ) {
        val uiModel = UINickNameListModel(
            showLoading,
            showError,
            showSuccess,
            showEnd,
            isRefresh
        )
        withContext(Dispatchers.Main) {
            _uiNickNameListState.value = uiModel
        }
    }

}