package com.metathought.food_order.casheir.ui.member.dialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.DialogInterface
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.hbb20.CountryCodePicker
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.UpdateConsumerType
import com.metathought.food_order.casheir.data.model.base.response_model.member.Record
import com.metathought.food_order.casheir.databinding.DialogMemberEditBinding
import com.metathought.food_order.casheir.extension.formatPhoneNumber
import com.metathought.food_order.casheir.extension.getRedStar
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.helper.LocaleHelper
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber


@AndroidEntryPoint
class EditMemberInfoDialog : BaseDialogFragment(), CountryCodePicker.DialogEventsListener {
    private var binding: DialogMemberEditBinding? = null
    private var editSuccess: (() -> Unit)? = null
    private var record: Record? = null
    private var type: UpdateConsumerType = UpdateConsumerType.NAME

    private val editMemberViewModel: EditMemberViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogMemberEditBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        openKeyBoardListener()
        onTouchOutSide(binding?.layoutMain)

        initData()
        initListener()
        initObserver()

    }

    @SuppressLint("SetTextI18n")
    private fun initObserver() {
        editMemberViewModel.uiState.observe(viewLifecycleOwner) {
            binding?.apply {
                if (it.success?.isSuccess() == true) {
                    editSuccess?.invoke()
                    dismissCurrentDialog()
                }
                if (!it.error.isNullOrEmpty()) {
                    if (type == UpdateConsumerType.NAME) {
                        Toast.makeText(context, "${it.error}", Toast.LENGTH_LONG).show()
                    } else {
                        textInputLayoutValidateCode.error = it.error
                    }
                }
            }
        }

        editMemberViewModel.uiCountdownState.observe(viewLifecycleOwner) {
            binding?.apply {
                if (it.success?.isSuccess() == true) {
                    Toast.makeText(
                        context,
                        getString(R.string.validate_code_sent),
                        Toast.LENGTH_LONG
                    ).show()
                }
                if (!it.error.isNullOrEmpty()) {
                    Toast.makeText(context, "${it.error}", Toast.LENGTH_LONG).show()
                }
                val validateCodeBtnEnabled = it.validateCodeBtnEnabled
                if (validateCodeBtnEnabled != null) {
                    tvGetValidateCode.isEnabled = validateCodeBtnEnabled
                    if (validateCodeBtnEnabled) {
                        tvGetValidateCode.text = getString(R.string.retrieve)
                    } else {
                        tvGetValidateCode.text =
                            "${it.countdownTime}s ${getString(R.string.retrieve)}"
                    }
                }
            }
        }

    }


    private fun initData() {
        binding?.apply {
            if (UpdateConsumerType.NAME == type) {
                tvTableID.text = getString(R.string.edit_nickname)
                textInputLayoutCustomerName.isVisible = true
                flPhoneNumber.isVisible = false
                flValidateCode.isVisible = false
            } else {
                tvTableID.text = getString(R.string.edit_phone_number)
                textInputLayoutCustomerName.isVisible = false
                flPhoneNumber.isVisible = true
                flValidateCode.isVisible = true
            }

            textInputLayoutCustomerName.hint =
                getString(R.string.customer_nickname_required).getRedStar(resources)
            textInputLayoutPhoneNumber.hint =
                getString(R.string.mobile_phone_number).getRedStar(resources)
            val lang = LocaleHelper.getLang(MyApplication.myAppInstance)
            if (lang.startsWith("zh")) {
                countryCodeHolder.changeDefaultLanguage(CountryCodePicker.Language.CHINESE_SIMPLIFIED)
            } else if (lang.startsWith("km")) {
                countryCodeHolder.changeDefaultLanguage(CountryCodePicker.Language.KHMER)
            } else {
                countryCodeHolder.changeDefaultLanguage(CountryCodePicker.Language.ENGLISH)
            }
            countryCodeHolder.supportFragementManager = activity?.supportFragmentManager
            checkEnable()
        }


    }

    private val ontextChange = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
        }

        override fun afterTextChanged(s: Editable?) {
            checkEnable()
        }

    }
    private val onTextChangePhoneNumber = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
        }

        override fun afterTextChanged(s: Editable?) {
            s?.let { editText(it) }
            binding?.apply {
                if (edtPhoneNumber.text.toString().replace(" ", "").length < 8) {
                    textInputLayoutPhoneNumber.error =
                        getString(R.string.invalid_format_enter_8_to_12_digits)
                    textInputLayoutPhoneNumber.isErrorEnabled = true
                } else if (edtPhoneNumber.text.toString()
                        .replace(" ", "").length >= 8
                ) {
                    textInputLayoutPhoneNumber.error = ""
                    textInputLayoutPhoneNumber.isErrorEnabled = false
                }
                checkEnable()
            }
        }

    }

    private fun initListener() {
        binding?.apply {
            countryCodeHolder.setDialogEventsListener(this@EditMemberInfoDialog)
//            countryCodeHolder.setTextSize(DisplayUtils.dp2px(requireContext(),10f))

            edtPhoneNumber.addTextChangedListener(onTextChangePhoneNumber)
            edtCustomerName.addTextChangedListener(ontextChange)
            edtPhoneNumber.addTextChangedListener(ontextChange)
            edtValidateCode.addTextChangedListener(ontextChange)
            edtValidateCode.addTextChangedListener {
                textInputLayoutValidateCode.error = null
            }

            btnClose.setOnClickListener {
                dismissCurrentDialog()
            }
            btnConfirm.setOnClickListener {
                if (UpdateConsumerType.NAME == type) {
                    editMemberViewModel.editMemberName(
                        record?.id ?: "",
                        edtCustomerName.text.toString().trim()
                    )
                } else {
                    editMemberViewModel.editMemberPhone(
                        record?.id ?: "",
                        countryCodeHolder.selectedCountryCode,
                        edtPhoneNumber.text.toString().trim().replace(" ", ""),
                        edtValidateCode.text.toString().trim()
                    )
                }
            }
            tvGetValidateCode.setOnClickListener {
                val phone = edtPhoneNumber.text.toString().trim().replace(" ", "")
                if (phone.length < 8) {
                    return@setOnClickListener
                }
                //发送验证码
                editMemberViewModel.getValidateCode(
                    record?.id ?: "",
                    countryCodeHolder.selectedCountryCode,
                    phone
                )
            }

            edtCustomerName.setText(record?.nickName)
            edtCustomerName.setSelection(edtCustomerName.text?.length ?: 0)

//            val (areaCode, phoneNumber) = record?.telephone.getConsumePhoneNumber()
//            Timber.d("telephone: ${record?.telephone}, areaCode: $areaCode, phoneNumber: $phoneNumber")
//            edtPhoneNumber.setText(phoneNumber)
//            countryCodeHolder.setCountryForPhoneCode(areaCode.toIntOrNull() ?: 855)
        }
    }

    private fun checkEnable() {
        binding?.apply {
            btnConfirm.setEnableWithAlpha(
                if (type == UpdateConsumerType.NAME) {
                    edtCustomerName.text?.trim()
                        ?.isNotEmpty() == true
                } else {
                    Timber.e("edtPhoneNumber.text ${edtPhoneNumber.text}")
                    edtValidateCode.text?.trim()
                        ?.isNotEmpty() == true && edtPhoneNumber.text?.trim()
                        ?.isNotEmpty() == true && (edtPhoneNumber.text ?: "").trim().length >= 8
                }
            )
        }
    }

    companion object {
        private const val TAG = "EditMemberInfoDialog"
        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85

        fun showDialog(
            fragmentManager: FragmentManager,
            type: UpdateConsumerType,
            record: Record,
            editSuccess: (() -> Unit),
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment =
                newInstance(record, type, editSuccess)
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? EditMemberInfoDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            record: Record,
            type: UpdateConsumerType,
            editSuccess: (() -> Unit)
        ): EditMemberInfoDialog {
            val fragment = EditMemberInfoDialog()
            fragment.editSuccess = editSuccess
            fragment.record = record
            fragment.type = type

            return fragment
        }
    }

    override fun onCcpDialogOpen(dialog: Dialog?) {

    }

    override fun onCcpDialogDismiss(dialogInterface: DialogInterface?) {
        binding?.let {
            it.edtPhoneNumber.isFocusable = true
        }
    }

    override fun onCcpDialogCancel(dialogInterface: DialogInterface?) {
    }


    private fun editText(editable: Editable) {
        binding?.apply {
            edtPhoneNumber.removeTextChangedListener(onTextChangePhoneNumber)
            val formattedPhoneNumber = editable.toString().formatPhoneNumber()
            edtPhoneNumber.setText(formattedPhoneNumber)

            edtPhoneNumber.setSelection(edtPhoneNumber.text?.length ?: 0)
            edtPhoneNumber.addTextChangedListener(onTextChangePhoneNumber)
        }
    }
}
