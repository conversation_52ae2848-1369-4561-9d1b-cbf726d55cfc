package com.metathought.food_order.casheir.ui.member.dialog

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.metathought.food_order.casheir.constant.UpdateConsumerType
import com.metathought.food_order.casheir.data.model.base.BaseBooleanResponse
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.Repository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject


@HiltViewModel
class EditMemberViewModel
@Inject
constructor(private val repository: Repository) : ViewModel() {
    private val _uiState = MutableLiveData<UIModel>()
    val uiState get() = _uiState

    private val _uiCountdownState = MutableLiveData<UICountdownModel>()
    val uiCountdownState get() = _uiCountdownState

    fun editMemberName(consumerId: String, name: String) {
        viewModelScope.launch {
            val result = repository.updateConsumer(
                consumerId = consumerId,
                type = UpdateConsumerType.NAME.id,
                nickname = name,
            )
            if (result is ApiResponse.Success) {
                emitUiState(success = result.data)

            } else if (result is ApiResponse.Error) {
                emitUiState(error = result.message)
            }
        }
    }

    fun editMemberPhone(
        consumerId: String,
        areaCode: String,
        phoneNumber: String,
        validateCode: String
    ) {
        viewModelScope.launch {
            val result = repository.updateConsumer(
                consumerId = consumerId,
                type = UpdateConsumerType.PHONE.id,
                phone = "$areaCode$phoneNumber",
                otp = validateCode,
            )
            if (result is ApiResponse.Success) {
                emitUiState(success = result.data)

            } else if (result is ApiResponse.Error) {
                emitUiState(error = result.message)
            }
        }
    }

    private var countdownJob: Job? = null
    private val totalTime = 60 // 默认60秒
    private var countdownTime = 0
    fun getValidateCode(consumerId: String, areaCode: String, phoneNumber: String) {
        if (countdownJob?.isActive == true) return // 防止重复启动

        viewModelScope.launch {
            val result =
                repository.sendSmsCode(consumerId = consumerId, phone = "$areaCode$phoneNumber")
            if (result is ApiResponse.Success) {
                startCountdown()
                emitUiCountdownState(success = result.data)
            } else if (result is ApiResponse.Error) {
                emitUiCountdownState(error = result.message)
            }
        }
    }

    private fun startCountdown() {
        countdownTime = totalTime
        emitUiCountdownState(validateCodeBtnEnabled = false, countdownTime = countdownTime)

        countdownJob = viewModelScope.launch(Dispatchers.IO) {
            try {
                while (countdownTime > 0) {
                    delay(1000)
                    countdownTime--
                    emitUiCountdownState(
                        validateCodeBtnEnabled = false,
                        countdownTime = countdownTime
                    )
                }
            } catch (e: Exception) {
                Timber.e(e)
            } finally {
                emitUiCountdownState(validateCodeBtnEnabled = true)
            }
        }
    }


    private fun emitUiState(
        error: String? = null,
        success: BaseBooleanResponse? = null,
    ) {
        val uiModel = UIModel(error, success)
        _uiState.postValue(uiModel)
    }

    private fun emitUiCountdownState(
        error: String? = null,
        success: BaseBooleanResponse? = null,
        validateCodeBtnEnabled: Boolean? = null,
        countdownTime: Int = 0,
    ) {
        val uiModel = UICountdownModel(error, success, validateCodeBtnEnabled, countdownTime)
        _uiCountdownState.postValue(uiModel)
    }


    data class UIModel(
        val error: String?,
        val success: BaseBooleanResponse?
    )
    data class UICountdownModel(
        val error: String?,
        val success: BaseBooleanResponse?,
        var validateCodeBtnEnabled: Boolean? = null,
        var countdownTime: Int = 0,
    )
}
