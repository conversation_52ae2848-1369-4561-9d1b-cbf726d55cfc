package com.metathought.food_order.casheir.ui.dialog.store_report

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.FORMAT_DATE_TIME_SHOW
import com.metathought.food_order.casheir.constant.FORMAT_DATE_TIME_SHOW_WITH_SECOND
import com.metathought.food_order.casheir.constant.ReportDateType
import com.metathought.food_order.casheir.constant.ReportExportEnum
import com.metathought.food_order.casheir.data.model.base.response_model.report.SaleReportResponse
import com.metathought.food_order.casheir.databinding.DialogSalesReportBinding
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.formatDateStr
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.helper.FolderHelper
import com.metathought.food_order.casheir.helper.PermissionHelper
import com.metathought.food_order.casheir.helper.PreferenceHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.download.DownloadListener
import com.metathought.food_order.casheir.network.download.DownloadManager
import com.metathought.food_order.casheir.ui.adapter.SalesReportListAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.dialog.print_report.PrintReportViewModel
import com.metathought.food_order.casheir.utils.FileUtil
import com.metathought.food_order.casheir.utils.SingleClickUtils
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File
import java.math.BigDecimal
import java.util.Calendar
import java.util.Date


/**
 *<AUTHOR>
 *@time  2025/4/14
 *@desc 销售报表
 **/

@AndroidEntryPoint
class SalesReportDialog : BaseDialogFragment() {

    companion object {
        private const val TAG = "SalesReportDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance()
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? SalesReportDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
        ): SalesReportDialog {
            val args = Bundle()
            val fragment = SalesReportDialog()
            fragment.arguments = args
            return fragment
        }
    }

    private var binding: DialogSalesReportBinding? = null
    private val viewModel: PrintReportViewModel by viewModels()

    private var responseData: SaleReportResponse? = null

    //时间类型
    private var dateType = ReportDateType.YESTERDAY.id
    private var startTime = Date()
    private var endTime = Date()

    private var adapter = SalesReportListAdapter(ArrayList())

    private val searchRunnable = Runnable {
        try {
            binding?.apply {
                val list = tvCalendar.text.toString().split("-")
                if (list.size >= 2) {
                    viewModel.printSaleReport(
                        requireContext(),
                        startTime.formatDateStr(FORMAT_DATE_TIME_SHOW_WITH_SECOND),
                        endTime.formatDateStr(FORMAT_DATE_TIME_SHOW_WITH_SECOND),
                        edtSearch.getSearchContent()
                    )
                }
            }
        } catch (e: Exception) {

        }
    }

    private fun postSearch(duration: Int = 700) {
        binding?.apply {
            edtSearch.removeCallbacks(searchRunnable)
            if (duration <= 0) {
                searchRunnable.run()
            } else {
                edtSearch.postDelayed(searchRunnable, duration.toLong())
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogSalesReportBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        openKeyBoardListener()
        onTouchOutSide(binding?.root)

        initListener()
        initObserver()
        initView()
    }

    private fun initView() {
        binding?.apply {

            onlinePayment.text = "${onlinePayment.text}($)"
            offlinePayment.text = "${offlinePayment.text}($)"
            balancePayment.text = "${balancePayment.text}($)"
            creditPayment.text = "${creditPayment.text}($)"


            itemUnitPrice.text = "${itemUnitPrice.text}($)"
            itemServiceFee.text = "${itemServiceFee.text}($)"
            itemPackingFee.text = "${itemPackingFee.text}($)"
            itemOrderAmount.text = "${itemOrderAmount.text}($)"
            itemReduceAmount.text = "${itemReduceAmount.text}($)"
            itemVat.text = "${itemVat.text}($)"
            itemReceiveAmount.text = "${itemReceiveAmount.text}($)"

            tvTotalAmountTitle.text = "${tvTotalAmountTitle.text}($)"
            tvReduceAmountTitle.text = "${tvReduceAmountTitle.text}($)"
            tvActualReceiveAmountTitle.text = "${tvActualReceiveAmountTitle.text}($)"

            tvTotalOrderTitle.text = tvTotalOrderTitle.text.toString().uppercase()
            tvTotalAmountTitle.text = tvTotalAmountTitle.text.toString().uppercase()
            tvReduceAmountTitle.text = tvReduceAmountTitle.text.toString().uppercase()
            tvActualReceiveAmountTitle.text = tvActualReceiveAmountTitle.text.toString().uppercase()

            initDate()

            rvList.adapter = adapter

            postSearch()
        }
    }

    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismissCurrentDialog()
            }

            tvCalendar.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    ReportDateSelectDialog.showDialog(
                        parentFragmentManager,
                        startTime,
                        endTime,
                        dateType
                    ) { startTime1, endTime1, dateType1 ->
                        startTime = startTime1
                        endTime = endTime1
                        tvCalendar.text = "${startTime.formatDateStr(FORMAT_DATE_TIME_SHOW)} - ${
                            endTime.formatDateStr(FORMAT_DATE_TIME_SHOW)
                        }"
                        dateType = dateType1

                        postSearch()
                    }
                }
            }

            edtSearch.setTextChangedListenerCallBack {
                postSearch()
            }

            tvClearFilter.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    reset()
                }
            }


            btnExport.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    if (responseData == null || responseData?.salesOrdersVo?.salesOrdersDetailVoList.isNullOrEmpty()) {
                        showToast(requireContext().getString(R.string.empty_data))
                        return@isFastDoubleClick
                    }
                    if (PermissionHelper.checkPermissions(requireActivity())) {
                        SelectExportTypeDialog.showDialog(parentFragmentManager) {
                            btnExport.setEnableWithAlpha(false)
                            viewModel.export(
                                requireContext(),
                                startTime.formatDateStr(FORMAT_DATE_TIME_SHOW_WITH_SECOND),
                                endTime.formatDateStr(FORMAT_DATE_TIME_SHOW_WITH_SECOND),
                                ReportExportEnum.SALES_REPORT.id,
                                it,
                                edtSearch.getSearchContent()
                            ) { url ->
                                if (url != null) {
                                    download(url)
                                } else {
                                    btnExport.setEnableWithAlpha(true)
                                }
                            }
                        }
                    } else {
                        PermissionHelper.requestPermissions(requireActivity())
                    }
                }
            }
        }
    }

    private fun initObserver() {
        viewModel.uiSaleModel.observe(viewLifecycleOwner) { state ->
            when (state.response) {
                is ApiResponse.Loading -> {
                    binding?.apply {
                        pbLoading.isVisible = true
                    }
                }

                is ApiResponse.Success -> {
                    binding?.apply {
                        responseData = state.response.data
                        pbLoading.isVisible = false

                        initData()
                    }
                }

                is ApiResponse.Error -> {
                    binding?.apply {
                        pbLoading.isVisible = false
                    }
                }

                else -> {

                }

            }
        }
    }

    private fun initDate() {
        binding?.apply {
            lifecycleScope.launch {
                val storeInfo = PreferenceHelper.getStoreInfo()
                val startCalendar = android.icu.util.Calendar.getInstance()
                if (storeInfo?.isSetOpenStartTime() == true) {
                    //如果有设置营业时间,判断今天的营业时间时候已经到了
                    if (storeInfo.isCurrentInOpenTime()) {
                        dateType = ReportDateType.TODAY.id
                        Timber.e("今天的营业时间已经到了")
                        //营业时间-当前时间
                        val startTimeList = storeInfo.getStartTime()
                        endTime = (startCalendar.clone() as android.icu.util.Calendar).time

                        //然后设置营业开始时间
                        startCalendar.set(android.icu.util.Calendar.HOUR_OF_DAY, startTimeList[0])
                        startCalendar.set(android.icu.util.Calendar.MINUTE, startTimeList[1])
                        startCalendar.set(android.icu.util.Calendar.SECOND, startTimeList[2])
                        startCalendar.set(android.icu.util.Calendar.MILLISECOND, 0)
                        startTime = (startCalendar.clone() as android.icu.util.Calendar).time

                    } else {
                        dateType = ReportDateType.YESTERDAY.id
                        //今天的营业时间还没到就显示昨天的数据
                        Timber.e("今天的营业时间还没到就显示昨天的数据")
                        val startTimeList = storeInfo.getStartTime()
                        startCalendar.add(android.icu.util.Calendar.DAY_OF_MONTH, -1) // 先将当前日期调整为昨天
                        //然后设置营业开始时间
                        startCalendar.set(android.icu.util.Calendar.HOUR_OF_DAY, startTimeList[0])
                        startCalendar.set(android.icu.util.Calendar.MINUTE, startTimeList[1])
                        startCalendar.set(android.icu.util.Calendar.SECOND, startTimeList[2])
                        startCalendar.set(android.icu.util.Calendar.MILLISECOND, 0)
                        startTime = (startCalendar.clone() as android.icu.util.Calendar).time

                        // 先将当前日期调整为今天  的营业开始时间为结束时间
                        startCalendar.add(android.icu.util.Calendar.DAY_OF_MONTH, +1)
                        startCalendar.set(android.icu.util.Calendar.MILLISECOND, 0)
                        endTime = (startCalendar.clone() as android.icu.util.Calendar).time
                    }
                } else {
                    //没设置营业 时间 显示昨天的数据
                    dateType = ReportDateType.YESTERDAY.id
                    startCalendar.add(android.icu.util.Calendar.DAY_OF_MONTH, -1) // 先将当前日期调整为昨天
                    // 获取昨天开始时间（零点整）
                    startCalendar.set(android.icu.util.Calendar.HOUR_OF_DAY, 0)
                    startCalendar.set(android.icu.util.Calendar.MINUTE, 0)
                    startCalendar.set(android.icu.util.Calendar.SECOND, 0)
                    startCalendar.set(android.icu.util.Calendar.MILLISECOND, 0)
                    startTime = (startCalendar.clone() as android.icu.util.Calendar).time

                    // 获取昨天结束时间（23点59分59秒）
                    startCalendar.set(android.icu.util.Calendar.HOUR_OF_DAY, 23)
                    startCalendar.set(android.icu.util.Calendar.MINUTE, 59)
                    startCalendar.set(android.icu.util.Calendar.SECOND, 59)
                    startCalendar.set(android.icu.util.Calendar.MILLISECOND, 999)
                    endTime = startCalendar.time
                }
            }

            tvCalendar.text = "${startTime.formatDateStr(FORMAT_DATE_TIME_SHOW)} - ${
                endTime.formatDateStr(FORMAT_DATE_TIME_SHOW)
            }"
        }
    }

    private fun initData() {
        binding?.apply {
            tvOnlineAmount.text = (responseData?.salesOrdersVo?.onlinePay?.toBigDecimalOrNull()
                ?: BigDecimal.ZERO).halfUp(2).decimalFormatTwoDigitZero()
            tvOfflineAmount.text = (responseData?.salesOrdersVo?.offlinePay?.toBigDecimalOrNull()
                ?: BigDecimal.ZERO).halfUp(2).decimalFormatTwoDigitZero()
            tvBalanceAmount.text = (responseData?.salesOrdersVo?.balancePay?.toBigDecimalOrNull()
                ?: BigDecimal.ZERO).halfUp(2).decimalFormatTwoDigitZero()
            tvCreditAmount.text = (responseData?.salesOrdersVo?.creditPay?.toBigDecimalOrNull()
                ?: BigDecimal.ZERO).halfUp(2).decimalFormatTwoDigitZero()


            tvTotalOrderNum.text = "${responseData?.salesOrdersVo?.totalOrderNum ?: 0}"
            tvTotalAmount.text = (responseData?.salesOrdersVo?.totalAmount?.toBigDecimalOrNull()
                ?: BigDecimal.ZERO).halfUp(2).decimalFormatTwoDigitZero()

            tvReduceAmount.text =
                (responseData?.salesOrdersVo?.totalDiscountAmount?.toBigDecimalOrNull()
                    ?: BigDecimal.ZERO).halfUp(2).decimalFormatTwoDigitZero()
            tvActualReceiveAmount.text =
                (responseData?.salesOrdersVo?.totalReceiveAmount?.toBigDecimalOrNull()
                    ?: BigDecimal.ZERO).halfUp(2).decimalFormatTwoDigitZero()

            if (responseData?.salesOrdersVo?.salesOrdersDetailVoList.isNullOrEmpty()) {
                llBottom.isVisible = false
                layoutEmpty.root.isVisible = true
                rvList.isVisible = false
            } else {
                llBottom.isVisible = true
                layoutEmpty.root.isVisible = false
                rvList.isVisible = true
                adapter.replaceData(
                    ArrayList(
                        responseData?.salesOrdersVo?.salesOrdersDetailVoList ?: listOf()
                    )
                )
            }
        }
    }

    private fun reset() {
        binding?.apply {
            edtSearch.setSearchContent("")
            initDate()
            postSearch()
        }
    }

    private fun download(url: String) {
        val fileName = FileUtil.getFileNameInUrl(url)
        val dir = FolderHelper.getDownloadFolderPath()
        DownloadManager.getInstance()
            .download(
                TAG,
                url,
                fileName,
                dir ?: "",
                object : DownloadListener {
                    override fun onProgress(progress: Long, max: Long) {
                        requireActivity().apply {
                            Timber.e("progress $progress")
                            runOnUiThread {
                                binding?.apply {
                                    val currentProgress =
                                        ((progress * 1.0f / max).times(100)).toInt()
                                }
                            }
                        }
                    }

                    override fun onSuccess(localPath: String) {
                        val file = File(dir, fileName)
                        val renameToRes = File(localPath).renameTo(file)
                        FileUtil.scanFile(requireActivity(), file)
                        requireActivity().runOnUiThread {
                            binding?.apply {
                                btnExport.setEnableWithAlpha(true)
                            }
                            Toast.makeText(
                                requireActivity(),
                                requireActivity().getString(
                                    R.string.save_at_location,
                                    file.absolutePath
                                ),
                                Toast.LENGTH_LONG
                            ).show()
                        }

                    }

                    override fun onFail(errorInfo: String) {
                        requireActivity().apply {
                            runOnUiThread {
                                binding?.apply {
                                    btnExport.setEnableWithAlpha(true)
                                }
                                if (errorInfo.isNotEmpty())
                                    Toast.makeText(context, errorInfo, Toast.LENGTH_LONG).show()
                            }
                        }
                    }
                })
    }


    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.85).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.95).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

}