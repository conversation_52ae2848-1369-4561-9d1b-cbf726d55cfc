package com.metathought.food_order.casheir.ui.dialog.pay_center

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import com.metathought.food_order.casheir.databinding.DialogPayCustomKhrInputBinding
import com.metathought.food_order.casheir.extension.hideKeyboard2
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.helper.PreferenceHelper
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import timber.log.Timber
import java.math.BigDecimal


/**
 *<AUTHOR>
 *@time  2025/3/31
 *@desc  修改瑞尔支付的时候自定义金额
 **/

@AndroidEntryPoint
class PayCustomKhrInputDialog : BaseDialogFragment() {

    companion object {
        private const val TAG = "PayCustomKhrInputDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            index: Int? = null,
            list: MutableList<Long>? = null,
            callBack: (() -> Unit)? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment =
                newInstance(
                    index,
                    list,
                    callBack
                )
            fragment.show(fragmentManager, TAG)
        }

        private fun newInstance(
            index: Int? = null,
            list: MutableList<Long>? = null,
            callBack: (() -> Unit)? = null
        ): PayCustomKhrInputDialog {
            val args = Bundle()
            val fragment = PayCustomKhrInputDialog()

            fragment.arguments = args
            fragment.index = index
            fragment.list = list
            fragment.callBack = callBack
            return fragment
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? PayCustomKhrInputDialog
            fragment?.dismissAllowingStateLoss()
        }
    }

    private var binding: DialogPayCustomKhrInputBinding? = null
    private var index: Int? = null
    private var list: MutableList<Long>? = null
    private var callBack: (() -> Unit)? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogPayCustomKhrInputBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        openKeyBoardListener()
        onTouchOutSide(binding?.root)
        initView()
        initListener()
        initObserver()
    }

    private fun initObserver() {

    }

    private fun initListener() {

        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismissAllowingStateLoss()
            }

            tvConfirm.setOnClickListener {
                lifecycleScope.launch {
                    if (!list.isNullOrEmpty()) {
                        list!![index!!] = edtAmount.text.toString().toLong()
                        PreferenceHelper.setCustomerInputKhrList(list!!)
                        callBack?.invoke()
                        dismissAllowingStateLoss()
                    }
                }
            }

            edtAmount.setOnClickListener {
                root.post {
                    hideKeyboard2()
                }
            }
            edtAmount.setOnFocusChangeListener { view, b ->
                if (b) {
                    root.post {
                        hideKeyboard2()
                    }
                }

                viewKeyBoard.setCurrentEditText(null)
                if (b) {
                    viewKeyBoard.setIsInit(true)
                    viewKeyBoard.setRange(
                        BigDecimal(1),
                        BigDecimal(999999),
                    )
                    viewKeyBoard.setCurrentEditText(edtAmount)
                }
            }

            edtAmount.addTextChangedListener {
                checkEnable()
            }

            edtAmount.post {
                edtAmount.requestFocus()
            }
        }
    }

    private fun initView() {
        binding?.apply {
            edtAmount.setText("${list!![index!!]}")

        }
    }

    private fun checkEnable() {
        binding?.apply {
            val amount = edtAmount.text.toString()
            if (amount.isNullOrEmpty()) {
                tvConfirm.setEnableWithAlpha(false)
            } else if ((amount.toBigDecimalOrNull()
                    ?: BigDecimal.ZERO).remainder(BigDecimal(100)) > BigDecimal.ZERO
            ) {
                tvConfirm.setEnableWithAlpha(false)
            } else if ((amount.toBigDecimalOrNull()
                    ?: BigDecimal.ZERO) >= BigDecimal.valueOf(100) && (amount.toBigDecimalOrNull()
                    ?: BigDecimal.ZERO) <= BigDecimal.valueOf(999900)
            ) {
                tvConfirm.setEnableWithAlpha(true)
            } else {
                tvConfirm.setEnableWithAlpha(false)
            }

        }
    }


    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.8).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.4).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

}