package com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate


import com.google.gson.annotations.SerializedName


data class PrintTamplateResponseItem(
    @SerializedName("createTime")
    val createTime: String?,
    @SerializedName("id")
    val id: String?,
    @SerializedName("informationShow")
    val informationShow: InformationShow?,
    @SerializedName("lang")//zh,en
    val lang: String?,
    @SerializedName("reservationDuration")
    val reservationDuration: Int?,
    @SerializedName("saasStoreId")
    val saasStoreId: String?,
    @SerializedName("servicePhone")
    val servicePhone: String?,
    @SerializedName("slogan")
    val slogan: String?,
    @SerializedName("storeId")
    val storeId: String?,
    @SerializedName("type")
    //Template types: 1- kitchen template; 2- dine-in template; 3- take-out template; 4- appointment template; 5- kiosk template
    val type: Int?,
    @SerializedName("updateTime")
    val updateTime: String?,

    @SerializedName("companyName")
    val companyName: String?,

    @SerializedName("companyTaxNumber")
    val companyTaxNumber: String?,

    @SerializedName("companyAddress")
    val companyAddress: String?,

    @SerializedName("companyContactNumber")
    val companyContactNumber: String?,

    @SerializedName("companyContactEmail")
    val companyContactEmail: String?,

    //1:厨打合并菜品打印 ；  2:厨打分别菜品打印
    @SerializedName("printerWay")
    val printerWay: Int?,

    //1:厨打合并菜品打印  2:厨打分别菜品打印   厨打 合并打印和分开打印可以同时     1,2
    @SerializedName("printerWayV2")
    var printerWayV2: String?,

    //打印份数
    @SerializedName("copies")
    val copies: Int?,

    //passApp优惠码
    @SerializedName("passAppPromotionCode")
    val passAppPromotionCode: String?,

    //多端厨房功能  用于区分菜品用哪个打印机打印
    @SerializedName("storeKitchenId")
    val storeKitchenId: String?,

    @SerializedName("storeKitchenName")
    val storeKitchenName: String?,

    /**
     * 是否显示发票号
     */
    @SerializedName("isNeedInvoiceNumber")
    val isNeedInvoiceNumber: Boolean?,
    /**
     * 是否显示税务信息
     */
    @SerializedName("isTicketShowTaxInfo")
    val isTicketShowTaxInfo: Boolean?,

    ) {

    //中文和英文
    fun isZhAndEn() = lang?.contains("zh") == true && lang.contains("en")

    //中文和柬文
    fun isZhAndKm() = lang?.contains("zh") == true && lang.contains("km")

    //英文和柬文
    fun isEnAndKm() = lang?.contains("en") == true && lang.contains("km")

    fun isZh() = lang == "zh"
    fun isKm() = lang == "km"
    fun isEn() = lang == "en"

    fun getLangList(): List<String> {
        return lang?.split(",") ?: listOf("en")
    }


}