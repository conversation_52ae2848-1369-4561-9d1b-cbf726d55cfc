package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.get
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.OrderCouponModel
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.UsageGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrintTamplateResponseItem
import com.metathought.food_order.casheir.databinding.ItemTicketOrderMenuBinding
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigit
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.getStringByLocale
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import timber.log.Timber
import java.util.Locale

/**
 * 优惠券赠品
 * Dine-in/Take-away/Booking to print receipt information
 * <AUTHOR>
 * @date 2024/09/1014:15
 * @description
 */
class PrinterTicketCouponZsMenuAdapter(
    val list: ArrayList<UsageGoods>,
    val currentOrderedInfo: OrderedInfoResponse?,
    val templateItem: PrintTamplateResponseItem,
    val isEightyWidth: Boolean?,  //是否80打印机
    val lang: Locale? = null
) :
    RecyclerView.Adapter<PrinterTicketCouponZsMenuAdapter.PrinterTicketMenuViewHolder>() {

    inner class PrinterTicketMenuViewHolder(val binding: ItemTicketOrderMenuBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(resource: UsageGoods, position: Int) {
            itemView.context.run {
                resource.let {
                    binding.apply {
                        val localeList = mutableListOf<Locale>()
                        val langList = templateItem.getLangList()
                        langList.forEach {
                            localeList.add(Locale(it.uppercase()))
                        }
                        if (localeList.isEmpty()) {
                            localeList.add(Locale("EN"))
                        }
                        var nameString = ""
                        localeList.forEachIndexed { index, lang ->
                            val foodName = it.getNameByLocal(lang)
                            if (isEightyWidth == true) {
                                //80的用这个
                                if (index == 0) {
                                    tvFoodNameEn.text = foodName
                                    tvFoodNameEn.isVisible = !foodName.isNullOrEmpty()
                                } else if (index == 1) {
                                    tvFoodNameKh.text = foodName
                                    tvFoodNameKh.isGone =
                                        tvFoodNameKh.text == tvFoodNameEn.text || tvFoodNameKh.text.isNullOrEmpty()
                                }
                            } else {
                                //58的用这个
                                if (nameString.isEmpty()) {
                                    nameString = foodName ?: ""
                                } else {
                                    if (nameString != foodName) {
                                        nameString = "$nameString $foodName"
                                    }
                                }
                            }
                        }

                        if (isEightyWidth == true) {
                            //80的用这个
                            //如果菜名一样就隐藏一个
                            llFoodName.isVisible = true
                            tvFoodName.isVisible = false
                            llItemIndex.isVisible = true
                            llFoodDiscountPrice.isVisible = true

                            (llItemIndex.layoutParams as LinearLayout.LayoutParams).weight = 1f
                            (llFoodName.layoutParams as LinearLayout.LayoutParams).weight = 2.5f
                            (llFoodCount.layoutParams as LinearLayout.LayoutParams).weight = 1f
                            (llFoodPrice.layoutParams as LinearLayout.LayoutParams).weight = 1.5f
                            (llFoodDiscountPrice.layoutParams as LinearLayout.LayoutParams).weight =
                                1.5f
                            (llFoodTotalPrice.layoutParams as LinearLayout.LayoutParams).weight =
                                1.5f

                            tvIndex.text =
                                "${(currentOrderedInfo?.goods?.count() ?: 0) + position + 1}"
                        } else {
                            //58的用这个
//                            if ((it.weight ?: 0.0) > 0.0) {
//                                nameString =
//                                    "${nameString}(${it.getWeightStr()})"
//                            }

                            tvFoodName.text = nameString
                        }


                        specRecyclerView.isGone = true


                        feedRecyclerView.isGone = true


                        tvFoodCount.text = "x1"

                        tvFoodPrice.text =
                            "${resource.getPrice()}"
                        if (resource.isTimePriceGood()) {
                            tvFoodPrice.text =
                                tvFoodPrice.context.getStringByLocale(R.string.time_price, lang!!)
                        }

                        tvFoodTotalPrice.text =
                            tvFoodTotalPrice.context.getStringByLocale(R.string.free, lang!!)

                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = PrinterTicketMenuViewHolder(
        ItemTicketOrderMenuBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(holder: PrinterTicketMenuViewHolder, position: Int) {
        holder.bind(list[position], position)
    }

    fun replaceData(
        newData: ArrayList<UsageGoods>
    ) {
        if (newData.isNotEmpty()) {
            list.clear()
            list.addAll(newData)
            notifyDataSetChanged()
        }
    }

}