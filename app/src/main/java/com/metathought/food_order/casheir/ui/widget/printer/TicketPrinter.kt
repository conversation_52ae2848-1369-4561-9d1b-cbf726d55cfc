package com.metathought.food_order.casheir.ui.widget.printer

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.util.TypedValue
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.get
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import com.github.alexzhirkevich.customqrgenerator.QrData
import com.github.alexzhirkevich.customqrgenerator.vector.QrCodeDrawable
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.constant.PrintTemplateTypeEnum
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrintTamplateResponseItem
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrinterConfigInfo
import com.metathought.food_order.casheir.databinding.KitchenTicketPrinterBinding
import com.metathought.food_order.casheir.databinding.NewCasherTicketPrinterBinding
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.decimalFormatZeroDigit
import com.metathought.food_order.casheir.extension.getPrinterDiningStyleString
import com.metathought.food_order.casheir.extension.getSourcePlatformByLocale
import com.metathought.food_order.casheir.extension.getStringByLocale
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero1
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.helper.PaymentMethodHelper
import com.metathought.food_order.casheir.helper.PrinterDeviceHelper
import com.metathought.food_order.casheir.ui.adapter.PrinterKitchenTicketMenuAdapter
import com.metathought.food_order.casheir.ui.adapter.PrinterKitenTicketCouponZsMenuAdapter
import com.metathought.food_order.casheir.ui.adapter.PrinterTicketCouponZsMenuAdapter
import com.metathought.food_order.casheir.ui.adapter.PrinterTicketMenuAdapter
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.app_dashbord.selectPrinter
import com.metathought.food_order.casheir.utils.BitmapUtil
import com.sunmi.printerx.enums.PrinterInfo
import com.tencent.bugly.crashreport.CrashReport
import timber.log.Timber
import java.math.BigDecimal
import java.nio.charset.Charset
import java.util.Date
import java.util.Locale
import androidx.core.graphics.createBitmap
import com.metathought.food_order.casheir.constant.MerchantNameDisplayModeEnum

import com.metathought.food_order.casheir.utils.DisplayUtils


/**
 *<AUTHOR>
 *@time  2024/11/27
 *@desc 小票打印
 **/

object TicketPrinter {
//    val dateFormat = SimpleDateFormat("yyyy/MM/dd HH:mm:ss", Locale.US)

    private fun getPrinterEightyWidth(): Int {
        return if (Printer.isXPrinter()) {
            575
        } else {
            try {
                Timber.e(
                    "111111 ${
                        selectPrinter?.queryApi()?.getInfo(PrinterInfo.PAPER)
                    }"
                )
                selectPrinter?.queryApi()?.getInfo(PrinterInfo.PAPER)?.toInt() ?: 380
            } catch (e: Exception) {
                Timber.e(
                    "22222 ${
                        selectPrinter?.queryApi()?.getInfo(PrinterInfo.PAPER)
                    }"
                )
                380
            }
        }
    }

    private fun getPrinterWidth(): Int {
        return if (Printer.isXPrinter()) {
            380
        } else {
            try {
                selectPrinter?.queryApi()?.getInfo(PrinterInfo.PAPER)?.toInt() ?: 380
            } catch (e: Exception) {
                380
            }
        }
    }

    /**
     * 厨打小票
     *
     * @param context
     * @param printTemplateResponseItem
     * @param orderInfo
     * @param isPrinterAgain
     * @param printerConfigInfo
     * @return
     */
    @SuppressLint("SetTextI18n", "UseKtx")
    fun initKitchenBitmap(
        context: Context,
        printTemplateResponseItem: PrintTamplateResponseItem,
        orderInfo: OrderedInfoResponse?,
        isPrinterAgain: Boolean = false,
        printerConfigInfo: PrinterConfigInfo?,
        isMargePrint: Boolean = true
    ): Bitmap? {

        var createBitmap: Bitmap? = null
        orderInfo?.let { currentOrderedInfo ->
            val isEightyWidth = printerConfigInfo?.isEightyWidth() ?: false
            //有加购，优先打印加购
            val isOrderMore = currentOrderedInfo.currentOrderMoreList?.isNotEmpty() == true

            var list = getOrderedGoods(isOrderMore, currentOrderedInfo)

            val localeList = mutableListOf<Locale>()
            val langList = printTemplateResponseItem.getLangList()
            langList.forEach {
                localeList.add(Locale(it.uppercase()))
            }

            if (localeList.isEmpty()) {
                localeList.add(Locale("EN"))
            }

            //是否是预订单
            val isReservation = currentOrderedInfo.isReservation == true

            val orderTime = if (isReservation) {
                if (isOrderMore) {//加购打印当前时间
                    Printer.dateFormat.format(Date(System.currentTimeMillis()))
                } else {
                    currentOrderedInfo.getDingTime()
                }
            } else {
                if (isOrderMore) {
                    Printer.dateFormat.format(Date(System.currentTimeMillis()))
                } else {
                    if (isPrinterAgain) {
                        //补打取当前时间  //14246
                        Printer.dateFormat.format(Date(System.currentTimeMillis()))
                    } else {
                        currentOrderedInfo.createTime
                    }
                }
            }

            var width = getPrinterWidth()
            if (isEightyWidth == true) {
                width = getPrinterEightyWidth()
            }

            //厨打小票
            val binding = KitchenTicketPrinterBinding.inflate(LayoutInflater.from(context))
            binding.apply {
                tvSeriesNo.isVisible = !isMargePrint

                val finalList = getKitchenFinalList(list, printTemplateResponseItem)

                if (finalList.firstOrNull()?.seriesNo != null) {
                    tvSeriesNo.text = "NO:${finalList.firstOrNull()?.seriesNo}"
                }

                val giftList = currentOrderedInfo.giftGoods?.filter {
                    it.kitchenMaking == true && it.storeKitchenId == printTemplateResponseItem.storeKitchenId
                }

                if (giftList?.firstOrNull()?.seriesNo != null) {
                    tvSeriesNo.text = "NO:${giftList.firstOrNull()?.seriesNo}"
                }

                //是否满足打印赠品
                val isCanPrintZp =
                    currentOrderedInfo.getZsCouponValid() && !isOrderMore && currentOrderedInfo.isAfterPayStatus()

                if (isMargePrint) {
                    //如果是合并打印 没商品就不打了
                    if (finalList.isEmpty()) {
                        //厨打如果没有可打印的菜品 就不打印
                        Timber.e("厨打过滤掉 不打")
                        return null
                    }
                } else {
                    //如果是分开打印
                    Timber.e("list:${finalList.size}  giftList:${giftList?.size}")
                    if (finalList.isEmpty()) {
                        if (!isCanPrintZp) {
                            Timber.e("不满足打印厨打赠品")
                            return null
                        }
                    }
                }

                recyclerView.adapter =
                    PrinterKitchenTicketMenuAdapter(
                        finalList,
                        currentOrderedInfo,
                        templateItem = printTemplateResponseItem
                    )

                if (isCanPrintZp) {
                    Timber.e("打印赠品")
                    vLine.isVisible = finalList.isNotEmpty()
                    //厨打赠品只有已支付的时候才打
                    val printerKitenTicketCouponZsMenuAdapter =
                        PrinterKitenTicketCouponZsMenuAdapter(
                            if (isMargePrint) {
                                //不分开打走这个方法取值逻辑
                                val giftGoodsList = currentOrderedInfo.getGiftGoodsList()
                                    .filter { it.kitchenMaking == true && it.storeKitchenId == printTemplateResponseItem.storeKitchenId }
                                ArrayList(
                                    giftGoodsList.toMutableList()
                                )
                            } else {
                                //分开打的时候giftgoods 重新赋值了
                                ArrayList(
                                    giftList ?: mutableListOf()
                                )
                            },
                            currentOrderedInfo,
                            templateItem = printTemplateResponseItem,
                            isEightyWidth = isEightyWidth,
                            localeList.first()
                        )
                    rvZplist.adapter = printerKitenTicketCouponZsMenuAdapter

                    if (printerKitenTicketCouponZsMenuAdapter.list.isEmpty()) {
                        return null
                    }
                }

                localeList.forEachIndexed { index, lang ->
                    (llPrintAgain[index] as TextView).isVisible = true
                    (llPrintAgain[index] as TextView).text =
                        context.getStringByLocale(R.string.print_again_all_good, lang)
                    if (isOrderMore) {
                        (llPrintAgain[index] as TextView).text =
                            context.getStringByLocale(R.string.print_again_new_good, lang)
                    }
                    //只有厨打才有
                    (llPrintAgain[index] as TextView).isVisible = isPrinterAgain

                    llStoreName[index].isVisible = true
                    if (index == 0) {
                        tvFirstStoreName.text =
                            printTemplateResponseItem.storeKitchenName ?: context.getStringByLocale(
                                R.string.main_kitchen,
                                lang
                            )
                    } else if (index == 1) {
                        tvSecondStoreName.isVisible = false
                    }

                    (llDiningStyle[index] as TextView).isVisible = true
                    (llDiningStyle[index] as TextView).text = if (isReservation) {
                        context.getStringByLocale(R.string.print_title_pre_order, lang)
                    } else {
                        currentOrderedInfo.diningStyle?.getPrinterDiningStyleString(
                            context,
                            lang
                        )
                    }


                    (llTableNameTitle[index] as TextView).isVisible = true
                    (llTableNameTitle[index] as TextView).text =
                        context.getStringByLocale(R.string.print_title_table_name, lang)

                    (llDateTime[index] as TextView).isVisible = true
                    (llDateTime[index] as TextView).text =
                        context.getStringByLocale(R.string.print_title_datetime, lang)

                    (llRemarkTitle[index] as TextView).isVisible = true
                    (llRemarkTitle[index] as TextView).text =
                        context.getStringByLocale(R.string.print_title_remark, lang)
                }

                tvOrderTime.text = orderTime

                llDiningStyle.isInvisible =
                    printTemplateResponseItem.informationShow?.showOrderType != true

                tvPickUpNo.text = "${currentOrderedInfo.pickupCode}"
                tvPickUpNo.textSize =
                    printTemplateResponseItem.informationShow!!.getMealCodeFontRealSize(
                        context,
                        isEightyWidth = isEightyWidth
                    )
                tvPickUpNo.isVisible =
                    !currentOrderedInfo.pickupCode.isNullOrEmpty() && printTemplateResponseItem.informationShow.showMealCode == true

                llOrderType.isVisible =
                    llDiningStyle.isInvisible || tvPickUpNo.isVisible


                llTableName.isVisible =
                    printTemplateResponseItem.informationShow.showTableName == true
                tvTableName.text = currentOrderedInfo.tableName
                tvTableName.textSize =
                    printTemplateResponseItem.informationShow.getTableNameFontRealSize(
                        context,
                        isEightyWidth = isEightyWidth
                    )


                llRemark.isVisible =
                    currentOrderedInfo.getFinalNote()
                        .isNotEmpty() && printTemplateResponseItem.informationShow.showNote == true
                tvRemark.text = currentOrderedInfo.getFinalNote()
                tvRemark.textSize =
                    printTemplateResponseItem.informationShow.getCookTicketMemoFontRealSize(
                        context,
                        isEightyWidth = isEightyWidth
                    )

                vFooter.thankYou.isVisible = false

                if (isEightyWidth == true) {
                    vFooter.root.setPadding(0, 0, 0, 30)
                } else {
                    vFooter.root.setPadding(0, 0, 0, 80)
                }

                //380 58mm
                //80mm 575
                root.measure(
                    View.MeasureSpec.makeMeasureSpec(
                        width,
                        View.MeasureSpec.EXACTLY
                    ), View.MeasureSpec.makeMeasureSpec(
                        0,
                        View.MeasureSpec.UNSPECIFIED
                    )
                )
                root.layout(0, 0, root.measuredWidth, root.measuredHeight)

                createBitmap =
                    createBitmap(root.measuredWidth, root.measuredHeight, Bitmap.Config.ARGB_4444)
                val canvas = Canvas(createBitmap!!).apply {
                    drawColor(Color.WHITE)
                }
                root.draw(canvas)
            }
        }
        //方法二
        return createBitmap
    }

    fun getOrderedGoods(
        isOrderMore: Boolean,
        currentOrderedInfo: OrderedInfoResponse
    ): ArrayList<OrderedGoods> {
        var list = ArrayList<OrderedGoods>()
        if (isOrderMore) {
            //过滤需要待称重的菜
            currentOrderedInfo.currentOrderMoreList?.filter { it.isHasProcessed() }
                ?.forEach {
                    list.add(it)
                }
            //打印设置完清空加购数据
        } else {
            //过滤需要待称重的菜
            currentOrderedInfo.goods?.filter { it.isHasProcessed() }
                ?.forEach {
                    list.add(it)
                }
        }
        return list
    }


    fun getKitchenFinalList(
        list: ArrayList<OrderedGoods>,
        printTemplateResponseItem: PrintTamplateResponseItem
    ): ArrayList<OrderedGoods> {
        var list1 = list
        list1 =
            ArrayList(list1.filter { (it.kitchenMaking == true && it.storeKitchenId == printTemplateResponseItem.storeKitchenId) || !it.orderMealSetGoodsDTOList.isNullOrEmpty() })
        val finalList = ArrayList<OrderedGoods>()
        //处理套餐打印
        list1.forEach { orderedGoods ->
            //如果是套餐
            if (!orderedGoods.orderMealSetGoodsDTOList.isNullOrEmpty()) {
                orderedGoods.orderMealSetGoodsDTOList =
                    orderedGoods.orderMealSetGoodsDTOList?.filter { it.kitchenMaking == true && it.storeKitchenId == printTemplateResponseItem.storeKitchenId }
                if (!orderedGoods.orderMealSetGoodsDTOList.isNullOrEmpty()) {
                    //最终套餐内 还有需要打的才打
                    finalList.add(orderedGoods)
                }
            } else {
                finalList.add(orderedGoods)
            }
        }
        return finalList
    }

    /**
     * 堂食/外带/预定/预结单/结账单
     *
     * @param context
     * @param printTemplateResponseItem
     * @param orderInfo
     * @param isPrinterAgain
     * @param paymentQrCode
     * @param isEightyWidth
     * @return
     */
    @SuppressLint("SetTextI18n", "UseKtx")
    fun initTicketBitmap(
        context: Context,
        printTemplateResponseItem: PrintTamplateResponseItem,
        orderInfo: OrderedInfoResponse?,
        isPrinterAgain: Boolean = false,
        paymentQrCode: String? = null,
        isEightyWidth: Boolean?,
//        isTaxAdministrationTicket: Boolean = false
    ): Bitmap? {

        var createBitmap: Bitmap? = null
        try {
            orderInfo?.let { currentOrderedInfo ->

                //有加购，优先打印加购
                val isOrderMore = currentOrderedInfo.currentOrderMoreList?.isNotEmpty() == true

                //是否结账小票
                val isCheckOut =
                    printTemplateResponseItem.type == PrintTemplateTypeEnum.CHECKOUT_RECEIPT.id

                //是否预结小票
                val isPreCheckOut =
                    printTemplateResponseItem.type == PrintTemplateTypeEnum.PRE_CHECKOUT_RECEIPT.id

                //是否预定单
                val isPreOrder =
                    printTemplateResponseItem.type == PrintTemplateTypeEnum.PRE_ORDER.id

                //是否是确认订单 Is it a confirmed order?
                var isConfirmed =
                    currentOrderedInfo.payStatus == OrderedStatusEnum.BE_CONFIRM.id && !isPreCheckOut

                //是否有余额支付
                val isBalancePayment =
                    currentOrderedInfo.payType == PayTypeEnum.USER_BALANCE.id || currentOrderedInfo.payType == PayTypeEnum.MIXED_PAYMENT.id
                //是否是线下渠道
                val isOfflinePayment =
                    currentOrderedInfo.payType == PayTypeEnum.CASH_PAYMENT.id || currentOrderedInfo.payType == PayTypeEnum.MIXED_PAYMENT.id
                //是否是现金付款
                val isCashPayment =
                    isOfflinePayment && currentOrderedInfo.isCash()


                /**
                 * 是否税务模式 如果发票号和 税务信息都打开 且 已支付 则打印  税务小票  非挂账-未支付
                 */
                val isTaxAdministrationTicket =
                    currentOrderedInfo.isOrderSuccess() && MainDashboardFragment.STORE_INFO?.ticketShowTaxInfo == true && MainDashboardFragment.STORE_INFO?.isNeedInvoiceNumber == true && !currentOrderedInfo.isCreditUnPaid()

                /**
                 * 是否有打开发票号 或者 税务信息其一  非挂账-未支付
                 */
                val isOpenInvoiceInfo =
                    currentOrderedInfo.isOrderSuccess() && (MainDashboardFragment.STORE_INFO?.ticketShowTaxInfo == true || MainDashboardFragment.STORE_INFO?.isNeedInvoiceNumber == true) && !currentOrderedInfo.isCreditUnPaid()


                Timber.e("是否确认单 $isConfirmed")

                Timber.e("是否结账小票 $isCheckOut")

                Timber.e("是否加购 $isOrderMore")

                val list = ArrayList<OrderedGoods>()
                if (isOrderMore) {
                    //过滤出已定价的菜
                    currentOrderedInfo.currentOrderMoreList?.filter { it.isHasProcessed() }
                        ?.forEach {
                            list.add(it)
                        }
                    //待支付加购的时候 也打确认单
                    isConfirmed = true
                    //打印设置完清空加购数据
                } else {
                    //过滤出已定价的菜
                    currentOrderedInfo.goods?.filter { it.isHasProcessed() }
                        ?.forEach {
                            list.add(it)
                        }
                }

                val totalGoodNum = list.fold(0) { acc, element ->
                    acc + (element.num ?: 0)
                }


                val localeList = mutableListOf<Locale>()
                val langList = printTemplateResponseItem.getLangList()
                langList.forEach {
                    localeList.add(Locale(it.uppercase()))
                }

                if (localeList.isEmpty()) {
                    localeList.add(Locale("EN"))
                }

                //是否是预订单
                val isReservation = currentOrderedInfo.isReservation == true


                val orderTime = if (isOrderMore) {
                    Printer.dateFormat.format(Date(System.currentTimeMillis()))
                } else {
                    if (currentOrderedInfo.isOrderSuccess()) {
                        currentOrderedInfo.payTime
                    } else {
                        currentOrderedInfo.createTime
                    }
                }


                var width = getPrinterWidth()
                if (isEightyWidth == true) {
                    width = getPrinterEightyWidth()
                }

                val binding = NewCasherTicketPrinterBinding.inflate(LayoutInflater.from(context))
                binding.apply {
//                    if (isEightyWidth == true) {
//                        setAllTextViewTextSize(
//                            binding.root.rootView,
//                            DisplayUtils.px2sp(
//                                context,
//                                context.resources.getDimension(R.dimen._printer_eight_default_sp)
//                            ).toFloat()
//                        )
//                        Timber.e("设置80字体大小完成 ${context.resources.getDimension(R.dimen._printer_eight_default_sp)}")
//                    } else {
//                        Timber.e("默认58字体大小完成 ${context.resources.getDimension(R.dimen._printer_default_sp)}")
//                    }

                    /**
                     *  如果是80的 横向布局
                     */
                    if (isEightyWidth == true) {
                        llItemIndex.isVisible = true
                        llDiscountPrice.isVisible = true
                        (llItemIndex.layoutParams as LinearLayout.LayoutParams).weight = 1f
                        (llItemName.layoutParams as LinearLayout.LayoutParams).weight = 2.5f
                        (llQty.layoutParams as LinearLayout.LayoutParams).weight = 1f
                        (llUnitPrice.layoutParams as LinearLayout.LayoutParams).weight = 1.5f
                        (llDiscountPrice.layoutParams as LinearLayout.LayoutParams).weight = 1.5f
                        (llItemTotal.layoutParams as LinearLayout.LayoutParams).weight = 1.5f

                        llTableNameTitle.orientation = LinearLayout.HORIZONTAL
                        llPickUpNoTitle.orientation = LinearLayout.HORIZONTAL
                        llPickUpNo.gravity = Gravity.CENTER_VERTICAL
                        llReserveTimeTitle.orientation = LinearLayout.HORIZONTAL
                        llReserveTime.gravity = Gravity.CENTER_VERTICAL
                        llTakeOutPlatformTitle.orientation = LinearLayout.HORIZONTAL
                        llTakeOutIdTitle.orientation = LinearLayout.HORIZONTAL
                        llDateTimeTitle.orientation = LinearLayout.HORIZONTAL
                        llInvoiceNumberTitle.orientation = LinearLayout.HORIZONTAL
                        llOrderTypeTitle.orientation = LinearLayout.HORIZONTAL
                        llOrderNoTitle.orientation = LinearLayout.HORIZONTAL
                        llCustomerTypeTitle.orientation = LinearLayout.HORIZONTAL
                        llCashierTitle.orientation = LinearLayout.HORIZONTAL
                        llTableNameWithInvoiceTitle.orientation = LinearLayout.HORIZONTAL
                        llPickUpNoWithInvoiceTitle.orientation = LinearLayout.HORIZONTAL
                        llPickUpNoWithInvoice.gravity = Gravity.CENTER_VERTICAL
                        llReserveTimeWithInvoiceTitle.orientation = LinearLayout.HORIZONTAL
                        llReserveTimeWithInvoiceTitle.gravity = Gravity.CENTER_VERTICAL
                        llNumberOfPaxTitle.orientation = LinearLayout.HORIZONTAL
                        llCustomerNameTitle.orientation = LinearLayout.HORIZONTAL
                        llRemarkTitle.orientation = LinearLayout.HORIZONTAL
                        llSubtotalTitle.orientation = LinearLayout.HORIZONTAL
                        llCouponActivityTitle.orientation = LinearLayout.HORIZONTAL
                        llPackPriceTitle.orientation = LinearLayout.HORIZONTAL
                        llServiceFeeTitle.orientation = LinearLayout.HORIZONTAL
                        llVatTitle.orientation = LinearLayout.HORIZONTAL
                        llCommissionTitle.orientation = LinearLayout.HORIZONTAL
                        llCouponTitle.orientation = LinearLayout.HORIZONTAL
                        llDiscountTitle.orientation = LinearLayout.HORIZONTAL
                        llDiscountAmountTitle.orientation = LinearLayout.HORIZONTAL

//                    llTotalTitle
//                    llKhrTotalTitle

                        llReceivedTitle.orientation = LinearLayout.HORIZONTAL
                        llChangeTitle.orientation = LinearLayout.HORIZONTAL
                        llAccountBalanceTitle.orientation = LinearLayout.HORIZONTAL
//                    llPaymentTimeTitle.orientation = LinearLayout.HORIZONTAL
                        llSourceTitle.orientation = LinearLayout.HORIZONTAL
                        llServiceLineTitle.orientation = LinearLayout.HORIZONTAL

                    }


                    //根据语言顺序来
                    localeList.forEachIndexed { index, lang ->
                        (llPrintAgain[index] as TextView).isVisible = true
                        (llPrintAgain[index] as TextView).text =
                            context.getStringByLocale(R.string.print_again, lang)
                        //只有厨打才有
                        (llPrintAgain[index] as TextView).isVisible = isPrinterAgain && !isCheckOut


                        (llStoreName[index] as TextView).isVisible = true
                        (llStoreName[index] as TextView).text =
                            MainDashboardFragment.CURRENT_USER?.getStoreNameByLan(lang)


                        (llDiningStyle[index] as TextView).isVisible = true
                        (llDiningStyle[index] as TextView).text = if (isReservation) {
                            context.getStringByLocale(R.string.print_title_pre_order, lang)
                        } else {
                            currentOrderedInfo.diningStyle?.getPrinterDiningStyleString(
                                context,
                                lang
                            )
                        }


                        (llTakeOutPlatformTitle[index] as TextView).isVisible = true
                        (llTakeOutPlatformTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.take_out_platform, lang)

                        (llTakeOutIdTitle[index] as TextView).isVisible = true
                        (llTakeOutIdTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.take_out_order_id, lang)


                        (llOrderTypeTitle[index] as TextView).isVisible = true
                        (llOrderTypeTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_order_type, lang)


                        (llReceipt[index] as TextView).isVisible = true
                        if (isCheckOut || isPreOrder) {
                            //如果是结账小票
                            (llReceipt[index] as TextView).text =
                                context.getStringByLocale(R.string.print_title_paid, lang)
                            if (currentOrderedInfo.isCreditPaid()) {
                                /**
                                 * 挂账已支付
                                 */
                                (llReceipt[index] as TextView).text =
                                    context.getStringByLocale(R.string.credit_paid, lang)
                            }
                            if (currentOrderedInfo.isCreditUnPaid()) {
                                /**
                                 * 挂账未支付
                                 */
                                (llReceipt[index] as TextView).text =
                                    context.getStringByLocale(R.string.credit_unpaid, lang)
                            }
                        } else if (isPreCheckOut) {
                            (llReceipt[index] as TextView).text =
                                context.getStringByLocale(
                                    R.string.print_title_to_be_paid,
                                    lang
                                )
                        } else if (currentOrderedInfo.isToBePaid()) {
                            (llReceipt[index] as TextView).text =
                                context.getStringByLocale(R.string.unpaid, lang)
                        } else if (isConfirmed) {
                            (llReceipt[index] as TextView).text =
                                context.getStringByLocale(R.string.print_title_confirmed, lang)
                        }

                        (llTicketTitle[index] as TextView).isVisible = true
                        if (isTaxAdministrationTicket) {
                            (llTicketTitle[index] as TextView).text =
                                context.getStringByLocale(
                                    R.string.print_title_invoice,
                                    lang
                                )
                        } else {
                            (llTicketTitle[index] as TextView).text =
                                context.getStringByLocale(R.string.print_title_ticket, lang)
                        }


                        (llPickUpNoTitle[index] as TextView).isVisible = true
                        (llPickUpNoTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_pick_up_no, lang)

                        (llPickUpNoWithInvoiceTitle[index] as TextView).isVisible = true
                        (llPickUpNoWithInvoiceTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_pick_up_no, lang)

                        (llTableNameTitle[index] as TextView).isVisible = true
                        (llTableNameTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_table_name, lang)

                        (llTableNameWithInvoiceTitle[index] as TextView).isVisible = true
                        (llTableNameWithInvoiceTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_table_name, lang)

                        (llDateTimeTitle[index] as TextView).isVisible = true
                        (llDateTimeTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_datetime, lang)



                        (llReserveTimeTitle[index] as TextView).isVisible = true
                        (llReserveTimeTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_pre_order_time, lang)

                        (llReserveTimeWithInvoiceTitle[index] as TextView).isVisible = true
                        (llReserveTimeWithInvoiceTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_pre_order_time, lang)


                        (llCustomerTypeTitle[index] as TextView).isVisible = true
                        (llCustomerTypeTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_customer_type_type, lang)

                        (llCashierTitle[index] as TextView).isVisible = true
                        (llCashierTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_Cashier, lang)

                        (llNumberOfPaxTitle[index] as TextView).isVisible = true
                        (llNumberOfPaxTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_number_of_pax, lang)

                        (llCustomerNameTitle[index] as TextView).isVisible = true
                        (llCustomerNameTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.customer_name, lang)

                        (llItemIndex[index] as TextView).isVisible = true
                        (llItemIndex[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_item_index, lang)

                        (llItemName[index] as TextView).isVisible = true
                        (llItemName[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_item_name, lang)

                        (llQty[index] as TextView).isVisible = true
                        (llQty[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_qty, lang)

                        (llUnitPrice[index] as TextView).isVisible = true
                        (llUnitPrice[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_u_price, lang)

                        (llDiscountPrice[index] as TextView).isVisible = true
                        (llDiscountPrice[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_item_discount, lang)


                        (llItemTotal[index] as TextView).isVisible = true
                        (llItemTotal[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_item_total, lang)

                        (llCancelGoodsTitle[index] as TextView).isVisible = true
                        (llCancelGoodsTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.cancel_dish, lang)


                        (llRemarkTitle[index] as TextView).isVisible = true
                        (llRemarkTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_remark, lang)

                        (llSubtotalTitle[index] as TextView).isVisible = true
                        (llSubtotalTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_subtotal, lang)

                        (llCouponActivityTitle[index] as TextView).text =
                            currentOrderedInfo.getCouponActivityNameByLocal(context, lang)
                        if (index == 1) {
                            couponActivityTitle2.isVisible =
                                couponActivityTitle1.text != couponActivityTitle2.text
                        }

                        (llVatTitle[index] as TextView).isVisible = true
                        (llVatTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_vat, lang)
                        if (index == 1) {
                            tvVat1.isVisible = tvVat0.text != tvVat1.text
                        }


                        (llServiceFeeTitle[index] as TextView).isVisible = true
                        (llServiceFeeTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_service_fee, lang)


                        (llCouponTitle[index] as TextView).isVisible = true
                        (llCouponTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_coupon, lang)


                        (llDiscountTitle[index] as TextView).isVisible = true
                        (llDiscountTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.discounts2, lang)

                        (llDiscountAmountTitle[index] as TextView).isVisible = true
                        (llDiscountAmountTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.discounts2, lang)

                        (llPackPriceTitle[index] as TextView).isVisible = true
                        (llPackPriceTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_pack_fee, lang)

                        (llCommissionTitle[index] as TextView).isVisible = true
                        (llCommissionTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.commission, lang)


                        (llTotalTitle[index] as TextView).isVisible = true
                        (llKhrTotalTitle[index] as TextView).isVisible = true
                        if (isTaxAdministrationTicket) {
                            (llTotalTitle[index] as TextView).text =
                                context.getStringByLocale(
                                    R.string.print_title_usd_total_with_invoice,
                                    lang
                                )
                            (llKhrTotalTitle[index] as TextView).text =
                                context.getStringByLocale(
                                    R.string.print_title_khr_total_with_invoice,
                                    lang
                                )
                        } else {
                            if (index == 0) {
                                (llTotalTitle[index] as TextView).text =
                                    "${
                                        context.getStringByLocale(
                                            R.string.print_title_total,
                                            lang
                                        )
                                    }($)"
                            } else {
                                (llTotalTitle[index] as TextView).text =
                                    context.getStringByLocale(R.string.print_title_total, lang)
                            }
                        }
//
//                        (llReceivedTitle[index] as TextView).isVisible = true
                        (llReceivedTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_received, lang)

//                        (llChangeTitle[index] as TextView).isVisible = true
                        (llChangeTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_change, lang)

//                        (llPaymentBalanceTitle[index] as TextView).isVisible = true
                        (llPaymentBalanceTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.pay_by_balance, lang)

//                        (llAccountBalanceTitle[index] as TextView).isVisible = true
                        (llAccountBalanceTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.customer_balance, lang)


                        (llOrderNoTitle[index] as TextView).isVisible = true
                        (llOrderNoTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_order_no, lang)

                        (llSourceTitle[index] as TextView).isVisible = true
                        (llSourceTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_customer_name, lang)


                        (llServiceLineTitle[index] as TextView).isVisible = true
                        (llServiceLineTitle[index] as TextView).text =
                            context.getStringByLocale(R.string.print_title_service_line, lang)


                        (llInvoiceNumberTitle[index] as TextView).isVisible = true
                        if (index == 0) {
                            tvInvoiceNumber0.text =
                                context.getStringByLocale(R.string.print_title_invoiceNumber, lang)
                        } else if (index == 1) {
                            val desc =
                                context.getStringByLocale(R.string.print_title_invoiceNumber, lang)
                            tvInvoiceNumber1.isVisible = desc != tvInvoiceNumber0.text
                            tvInvoiceNumber1.text = desc
                        }

                        if (index == 0) {
                            (llPassAppCouponTitle[index] as TextView).text =
                                context.getStringByLocale(R.string.pass_app_promo_code, lang)
                        }
                    }
                    val logoBase64 = PrinterDeviceHelper.getLogo()
                    ivLogo.isVisible =
                        printTemplateResponseItem.informationShow?.showMerchantLogo == true
//                    var blackBitmap: Bitmap? = null
                    if (!logoBase64.isNullOrEmpty() && ivLogo.isVisible) {
                        val logoBitmap = BitmapUtil.base64ToBitmap(logoBase64)
                        if (logoBitmap != null) {
//                            blackBitmap = BitmapUtil.convertToMonochromeBitmap(logoBitmap)
                            ivLogo.setImageBitmap(logoBitmap)
                        } else {
                            ivLogo.isVisible = false
                        }
                    } else {
                        ivLogo.isVisible = false
                    }

                    llCustomerType.isVisible =
                        printTemplateResponseItem.informationShow?.showCustomerTye == true

                    llStoreName.isVisible =
                        printTemplateResponseItem.informationShow?.showMerchantName == true
                    //隐藏 第二语言店名
                    tvSecondStoreName.isVisible = false
                    if (printTemplateResponseItem.informationShow?.showMerchantName == true) {
                        when (printTemplateResponseItem.informationShow?.merchantNameDisplayMode) {
                            MerchantNameDisplayModeEnum.MULTI_LANGUAGE_HORIZONTAL.id -> {
                                tvFirstStoreName.text =
                                    "${tvFirstStoreName.text} ${tvSecondStoreName.text}"
                            }

                            MerchantNameDisplayModeEnum.MULTI_LANGUAGE_VERTICAL.id -> {
                                Timber.e(" ${tvSecondStoreName.text}")
                                tvSecondStoreName.isVisible = true
                            }
                        }
                    }

                    //菜品
                    recyclerView.adapter =
                        PrinterTicketMenuAdapter(
                            list,
                            currentOrderedInfo,
                            isKitchen = false,
                            templateItem = printTemplateResponseItem,
                            isEightyWidth = isEightyWidth
                        )

                    if (currentOrderedInfo.getZsCouponValid()) {
                        //赠品券有效就打
                        rvZplist.adapter =
                            PrinterTicketCouponZsMenuAdapter(
                                ArrayList(currentOrderedInfo.getGiftGoodsList().toMutableList()),
                                currentOrderedInfo,
                                templateItem = printTemplateResponseItem,
                                isEightyWidth = isEightyWidth,
                                localeList.first()
                            )
                    }


                    llTicketTitle.isVisible = isTaxAdministrationTicket

                    tvOrderTime.text = orderTime

                    tvOrderNo.text = currentOrderedInfo.orderNo ?: ""
                    llOrderNo.isVisible =
                        printTemplateResponseItem.informationShow?.showOrderNo == true && tvOrderNo.text.isNotEmpty()

                    tvCustomerType.text =
                        context.getStringByLocale(R.string.walk_in, localeList.first())

                    val cashierName = currentOrderedInfo.employeeName

                    tvCashierContent.text = cashierName ?: ""
                    llCashier.isVisible =
                        printTemplateResponseItem.informationShow?.showCashierName == true && tvCashierContent.text.isNotEmpty()

                    tvNumberOfPax.text =
                        "${currentOrderedInfo.customerInfoVo?.diningNumber ?: currentOrderedInfo.peopleNum ?: 0}"
                    llNumberOfPax.isVisible =
                        tvNumberOfPax.text != "0" && printTemplateResponseItem.informationShow?.showNumberOfPax == true

                    tvCustomerName.text = currentOrderedInfo.getLastCustomerName()
                    llCustomerName.isVisible =
                        tvCustomerName.text.isNotEmpty() && printTemplateResponseItem.informationShow?.showCustomerName == true


                    if (currentOrderedInfo.isTakeOut()) {
                        llTakeOutId.isVisible = true
                        tvTakeOutId.text = currentOrderedInfo.deliveryOrderNo
                        llTakeOutPlatform.isVisible = true
                        tvTakeOutPlatform.text = currentOrderedInfo.deliveryPlatformName
                    }


                    llRemark.isVisible =
                        currentOrderedInfo.getFinalNote()
                            .isNotEmpty() && printTemplateResponseItem.informationShow?.showNote == true
                    tvRemark.text = currentOrderedInfo.getFinalNote()

                    //如果有减免折扣 则显示小计

                    var subtotal = currentOrderedInfo.getSubTotal()
                    //当前订单没有VAT、打包费、减免折扣，理应不显示小计字段
                    llSubtotal.isVisible = true
                    if (isConfirmed) {
                        //确认单显示总货物数
                        if (tvSubtotal1.isVisible) {
                            tvSubtotal1.text =
                                "${tvSubtotal1.text}(${totalGoodNum})"
                        } else {
                            tvSubtotal0.text =
                                "${tvSubtotal0.text}(${totalGoodNum})"
                        }
                        subtotal = 0
                        list.forEach {
                            subtotal += it.totalPriceWithSingleDiscount()
                        }
                    } else if (isOrderMore) {
                        subtotal = 0
                        list.forEach {
                            subtotal += it.totalPrice()
                        }
                    }

                    tvSubtotalUsdPrice.text = subtotal.priceFormatTwoDigitZero2()

                    //优惠活动 （未0就不显示）
                    llCouponActivity.isVisible =
                        !currentOrderedInfo.couponActivityList.isNullOrEmpty() && currentOrderedInfo.getTotalCouponActivityAmount() > 0
                    tvDiscountActivityAmount.text = "-${
                        currentOrderedInfo.getTotalCouponActivityAmount()
                            .priceFormatTwoDigitZero2()
                    }"
                    //如果是外带有打包费（未0就不显示）
                    llPackPrice.isVisible = currentOrderedInfo.getPackFee() > 0
                    tvPackUsdPrice.text = currentOrderedInfo.getPackFee().priceFormatTwoDigitZero2()

                    //服务费  改成和增值税 一样 有开服务费 0的时候也显示
                    val serviceChargePercentage = currentOrderedInfo.getServicePercentage()
                    llServiceFee.isVisible =
                        (MainDashboardFragment.CURRENT_USER?.getCurrentServiceChargePercentage()
                            ?: 0) > 0 && !currentOrderedInfo.isTakeAway() && !currentOrderedInfo.isTakeOut()
                    if (isEightyWidth == true) {
                        if (tvServiceFee1.isVisible) {
                            tvServiceFee1.text =
                                "${tvServiceFee1.text}(${serviceChargePercentage}%)"
                        } else {
                            tvServiceFee0.text =
                                "${tvServiceFee0.text}(${serviceChargePercentage}%)"
                        }
                    } else {
                        tvServiceFee0.text =
                            "${tvServiceFee0.text}(${serviceChargePercentage}%)"
                    }
                    tvServiceFeePrice.text =
                        currentOrderedInfo.getRealServiceFeePrice().priceFormatTwoDigitZero2()


                    /**
                     * 如果有开税率 就一定显示
                     */
                    val vatPercentage = currentOrderedInfo.price?.getVatPercentage()
                        ?: MainDashboardFragment.CURRENT_USER?.vatPercentage ?: 0

                    llVat.isVisible = (MainDashboardFragment.CURRENT_USER?.vatPercentage ?: 0) > 0
                    if (isEightyWidth == true) {
                        if (tvVat1.isVisible) {
                            tvVat1.text = "${tvVat1.text}(${vatPercentage}%)"
                        } else {
                            tvVat0.text =
                                "${tvVat0.text}(${vatPercentage}%)"
                        }
                    } else {
                        tvVat0.text =
                            "${tvVat0.text}(${vatPercentage}%)"
                    }
                    tvVatUsdPrice.text =
                        currentOrderedInfo.getRealVatPrice().priceFormatTwoDigitZero2()

                    if (currentOrderedInfo.isSetWholeDiscount()) {
                        if (currentOrderedInfo.discountReduceActivity != null) {
                            if (currentOrderedInfo.discountReduceActivity?.isPercentDiscount() == true) {
                                llDiscount.isVisible = true
                                tvDiscountPrice.text = "-${
                                    FoundationHelper.getPriceStrByUnit(
                                        currentOrderedInfo.conversionRatio ?: FoundationHelper.conversionRatio,
                                        currentOrderedInfo.getWholePercentDiscountAmount()
                                            .times(BigDecimal.valueOf(100))
                                            .toLong(),
                                        currentOrderedInfo.isKhr()
                                    )
                                }"
                                tvDiscountVipPrice.text = "VIP -${
                                    FoundationHelper.getPriceStrByUnit(
                                        currentOrderedInfo.conversionRatio ?: FoundationHelper.conversionRatio,
                                        currentOrderedInfo.getVipWholePercentDiscountAmount()
                                            .times(BigDecimal.valueOf(100))
                                            .toLong(),
                                        currentOrderedInfo.isKhr()
                                    )
                                }"
                                tvDiscountVipPrice.isVisible =
                                    isPreCheckOut && currentOrderedInfo.isShowVipPrice()
                                if (isEightyWidth == true) {
                                    if (tvDiscount1.isVisible) {
                                        tvDiscount1.text =
                                            "${tvDiscount1.text}(${currentOrderedInfo.getWholePercentDiscountStr()})"
                                    } else {
                                        tvDiscount0.text =
                                            "${tvDiscount0.text}(${currentOrderedInfo.getWholePercentDiscountStr()})"
                                    }
                                } else {
                                    tvDiscount0.text =
                                        "${tvDiscount0.text}(${currentOrderedInfo.getWholePercentDiscountStr()})"
                                }
//                                tvDiscountTitle.text =
//                                    "${getString(R.string.discounts2)}(${it.getWholePercentDiscountStr()})"
                            } else if (currentOrderedInfo.discountReduceActivity?.isFixedAmount() == true) {
                                //设置的是整单减免
                                llDiscountAmount.isVisible = true
//
                                tvDiscountAmountPrice.text = "-${
                                    FoundationHelper.getPriceStrByUnit(
                                        currentOrderedInfo.conversionRatio ?: FoundationHelper.conversionRatio,
                                        currentOrderedInfo.getNormalWholeItemReduceDollar()
                                            .times(BigDecimal(100))
                                            .toLong(),
                                        currentOrderedInfo.isKhr()
                                    )
                                }"
                                tvDiscountAmountVipPrice.text = "VIP -${
                                    FoundationHelper.getPriceStrByUnit(
                                        currentOrderedInfo.conversionRatio ?: FoundationHelper.conversionRatio,
                                        currentOrderedInfo.getNormalWholeItemReduceVipDollar()
                                            .times(BigDecimal(100))
                                            .toLong(),
                                        currentOrderedInfo.isKhr()
                                    )
                                }"

                                tvDiscountAmountVipPrice.isVisible =
                                    isPreCheckOut && currentOrderedInfo.isShowVipPrice()
                            }
                        } else {
                            if (currentOrderedInfo.getWholePercentDiscount() > BigDecimal.ZERO) {
                                //设置的是整单百分比折扣
                                llDiscount.isVisible = true
                                if (isEightyWidth == true) {
                                    if (tvDiscount1.isVisible) {
                                        tvDiscount1.text =
                                            "${tvDiscount1.text}(${currentOrderedInfo.getWholePercentDiscountStr()})"
                                    } else {
                                        tvDiscount0.text =
                                            "${tvDiscount0.text}(${currentOrderedInfo.getWholePercentDiscountStr()})"
                                    }
                                } else {
                                    tvDiscount0.text =
                                        "${tvDiscount0.text}(${currentOrderedInfo.getWholePercentDiscountStr()})"
                                }
                                tvDiscountPrice.text = "-${
                                    FoundationHelper.getPriceStrByUnit(
                                        currentOrderedInfo.conversionRatio ?: FoundationHelper.conversionRatio,
                                        currentOrderedInfo.getWholePercentDiscountAmount()
                                            .times(BigDecimal(100))
                                            .toLong(),
                                        currentOrderedInfo.wholeDiscountReduce?.isCustomizeKhr() == true
                                    )
                                }"
                                tvDiscountVipPrice.text = "VIP -${
                                    FoundationHelper.getPriceStrByUnit(
                                        currentOrderedInfo.conversionRatio ?: FoundationHelper.conversionRatio,
                                        currentOrderedInfo.getVipWholePercentDiscountAmount()
                                            .times(BigDecimal(100))
                                            .toLong(),
                                        currentOrderedInfo.wholeDiscountReduce?.isCustomizeKhr() == true
                                    )
                                }"
                                tvDiscountVipPrice.isVisible =
                                    isPreCheckOut && currentOrderedInfo.isShowVipPrice()
                            }

                            if (currentOrderedInfo.isSetCustomizeReduce()) {
                                //设置的是整单减免
                                llDiscountAmount.isVisible = false
                                if (currentOrderedInfo.wholeDiscountReduce?.isCustomizeKhr() == true) {
                                    tvDiscountAmountPrice.text =
                                        "-៛${
                                            currentOrderedInfo.getNormalWholeItemReduceKhr()
                                                .decimalFormatZeroDigit()
                                        }"
                                    tvDiscountAmountPrice.isVisible =
                                        (currentOrderedInfo.getNormalWholeReduceKhr() > BigDecimal.ZERO)
                                    tvDiscountAmountVipPrice.text =
                                        "VIP -៛${
                                            currentOrderedInfo.getNormalWholeItemReduceVipKhr()
                                                .decimalFormatZeroDigit()
                                        }"
                                    tvDiscountAmountVipPrice.isVisible =
                                        isPreCheckOut && (currentOrderedInfo.getVipWholeReduceKhr() > BigDecimal.ZERO) && currentOrderedInfo.isShowVipPrice()
                                    if (tvDiscountAmountPrice.isVisible || tvDiscountAmountVipPrice.isVisible) {
                                        llDiscountAmount.isVisible = true
                                    }
                                } else {
                                    tvDiscountAmountPrice.text =
                                        "-${
                                            currentOrderedInfo.getNormalWholeItemReduceDollar()
                                                .priceFormatTwoDigitZero2()
                                        }"
                                    tvDiscountAmountPrice.isVisible =
                                        (currentOrderedInfo.getNormalWholeReduceDollar() > BigDecimal.ZERO)
                                    tvDiscountAmountVipPrice.text =
                                        "VIP -${
                                            currentOrderedInfo.getNormalWholeItemReduceVipDollar()
                                                .priceFormatTwoDigitZero2()
                                        }"
                                    tvDiscountAmountVipPrice.isVisible =
                                        isPreCheckOut && (currentOrderedInfo.getVipWholeReduceDollar() > BigDecimal.ZERO) && currentOrderedInfo.isShowVipPrice()
                                    if (tvDiscountAmountPrice.isVisible || tvDiscountAmountVipPrice.isVisible) {
                                        llDiscountAmount.isVisible = true
                                    }
                                }
                            }
                        }
                    }

                    val realPrice = currentOrderedInfo.getRealPayPrice()

                    val coupon = currentOrderedInfo.getCurrentCoupon()
                    Timber.e("realPrice:===>  ${currentOrderedInfo.getRealPayPrice()}  ${coupon?.isZsCoupon()}")
                    if (coupon != null && !currentOrderedInfo.getZsCouponValid()
                    ) {
                        if (isPreCheckOut) {
                            //预结算单打印 优惠券金额 逻辑
                            tvCouponPrice.text =
                                "-${
                                    coupon.couponPrice
                                        ?.priceFormatTwoDigitZero2()
                                }"
                            tvCouponPrice.isVisible = coupon.isValid == true
                            tvVipCouponPrice.text =
                                "VIP -${
                                    coupon.vipCouponPrice
                                        ?.priceFormatTwoDigitZero2()
                                }"
                            tvVipCouponPrice.isVisible =
                                coupon.isVipValid == true && currentOrderedInfo.isShowVipPrice()

                            llCoupon.isVisible =
                                tvCouponPrice.isVisible || tvVipCouponPrice.isVisible

                        } else if (isCheckOut || isPreOrder) {
                            llCoupon.isVisible = true
                            //已支付
                            tvCouponPrice.text =
                                "-${
                                    currentOrderedInfo.getCouponAmount().priceFormatTwoDigitZero2()
                                }"
                            tvVipCouponPrice.isVisible = false
                        }
                    } else {
                        llCoupon.isVisible = false
                    }


                    tvTotalUsdPrice.text = realPrice.priceFormatTwoDigitZero2()

                    val khr = BigDecimal(
                        realPrice.times(
                            currentOrderedInfo.conversionRatio ?: 0
                        ).div(10000.0)
                    ).halfUp(0).times(BigDecimal(100)).toLong()

                    tvConversionRatio.text =
                        "$ 1= ៛ ${currentOrderedInfo.conversionRatio?.decimalFormatZeroDigit()}"
                    tvConversionRatio.isVisible =
                        printTemplateResponseItem.informationShow?.showExchangeRateConversion == true
                    tvTotalKhrPrice.text =
                        " ៛${khr.decimalFormatZeroDigit()}"
                    llConversionRatio.isVisible = true

                    llDateTime.isVisible =
                        printTemplateResponseItem.informationShow?.showPayTime == true
                    /**
                     * 已支付 或者预订单
                     */
                    if (currentOrderedInfo.isOrderSuccess()) {
                        if (currentOrderedInfo.isFromKiosk()) {
                            tvSource.text = currentOrderedInfo.consumerName
                        } else {
                            tvSource.text =
                                currentOrderedInfo.sourcePlatform?.getSourcePlatformByLocale(
                                    context,
                                    localeList.first()
                                )
                        }
                        if (currentOrderedInfo.isTakeOut()) {
                            llCommission.isVisible = true
                            tvCommissionPrice.text = "-${
                                currentOrderedInfo.price?.getCommissionToLong()
                                    ?.priceFormatTwoDigitZero2()
                            }"
                        }

                        llSource.isVisible =
                            printTemplateResponseItem.informationShow?.showOrderSource == true && !tvSource.text.isNullOrEmpty()


                        //已支付打印 退菜  有退菜且需要打印
                        if (!currentOrderedInfo.removeGoodList.isNullOrEmpty() && printTemplateResponseItem.informationShow?.isShowRemoveGoods() == true) {
                            llCancelGoods.isVisible = true
                            rvCancelGoodList.adapter =
                                PrinterTicketMenuAdapter(
                                    currentOrderedInfo.removeGoodList ?: arrayListOf(),
                                    currentOrderedInfo,
                                    isKitchen = false,
                                    templateItem = printTemplateResponseItem,
                                    isEightyWidth = isEightyWidth,
                                    isCancelGoods = true
                                )
                            rvCancelGoodList.isVisible = true
                        }

                        /**
                         * 只要是状态已支付的/获取预订单 且有配置就打印 发票相关信息
                         */
                        Timber.e("打印结账小单相关内容")
                        tvInvoiceNumber.text = currentOrderedInfo.invoiceNumber
                        llInvoiceNumber.isVisible =
                            !tvInvoiceNumber.text.isNullOrEmpty() //&& printTemplateResponseItem.isNeedInvoiceNumber == true

                        llAccountBalance.isVisible = isBalancePayment
                        tvAccountBalance.text =
                            currentOrderedInfo.surplusStoreBalance?.priceFormatTwoDigitZero2()
                    }

                    //预结算单相关处理
                    if (isPreCheckOut) {
                        llCashier.isVisible = false

                        val isShowVipPrice = currentOrderedInfo.isShowVipPrice()
                        tvTotalVipPrice.text =
                            "VIP $${
                                currentOrderedInfo.getFinalTotalVipPrice().toDouble()
                                    .decimalFormatTwoDigitZero()
                            }"
                        tvTotalVipPrice.isVisible = isShowVipPrice

                        //优惠活动
                        tvDiscountActivityAmount.text = "-${
                            currentOrderedInfo.getTotalCouponActivityAmount()
                                .priceFormatTwoDigitZero2()
                        }"

                        val isVipMarkList =
                            currentOrderedInfo.couponActivityList?.filter { it.vipMark == true }

                        tvVipDiscountActivityAmount.isVisible =
                            currentOrderedInfo.getTotalVipCouponActivityAmount() > BigDecimal.ZERO && isShowVipPrice && !isVipMarkList.isNullOrEmpty()

                        tvVipDiscountActivityAmount.text = "VIP -${
                            currentOrderedInfo.getTotalVipCouponActivityAmount()
                                .priceFormatTwoDigitZero2()
                        }"

                        llCouponActivity.isVisible =
                            (currentOrderedInfo.getTotalCouponActivityAmount() > 0 || (currentOrderedInfo.getTotalVipCouponActivityAmount() > BigDecimal.ZERO && isShowVipPrice))


                        //显示二维码  且要支持线上支付  后付款堂食非通用桌
                        if (paymentQrCode != null && printTemplateResponseItem.informationShow?.showKhqrCode == true && PaymentMethodHelper.isSupportOnline() && MainDashboardFragment.CURRENT_USER?.isPaymentInAdvance == false && !currentOrderedInfo.isUniversalTable() && currentOrderedInfo.isDineIn() && !currentOrderedInfo.isFromKiosk()) {
                            Timber.e("paymentQrCode $paymentQrCode")
                            llPaymentQrCode.isVisible = true
                            ivOrderQr.setImageDrawable(
                                QrCodeDrawable(
                                    QrData.Url(paymentQrCode),
                                    charset = Charset.forName("UTF-8")
                                )
                            )
                        }
                    }

                    llPriceLayout.isVisible = currentOrderedInfo.isOrderSuccess() || isPreCheckOut
                    llPaidTotalLayout.isVisible =
                        currentOrderedInfo.isOrderSuccess() || isPreCheckOut


                    //如果是找零
                    val receiveAmount = currentOrderedInfo.getReceiveAmountToTicket()
                    val changeAmount = currentOrderedInfo.getChangeAmountToTicket()

                    if (currentOrderedInfo.isPayByMix()) {
                        //如果是混合支付
                        llPaymentBalance.isVisible = true
                        tvPaymentBalance.text =
                            currentOrderedInfo.balancePayAmount?.priceFormatTwoDigitZero1()
                        llReceived.isVisible = true
                        tvReceived0.text = currentOrderedInfo.getOfflinePayMethodByLocal(
                            context,
                            localeList.first()
                        )
                        if (isCashPayment) {
                            tvReceivedAmount.text = currentOrderedInfo.getReceiveAmountToTicket()
                        } else {
                            tvReceivedAmount.text =
                                currentOrderedInfo.offlinePayAmount?.priceFormatTwoDigitZero1()
                        }
                    } else if (currentOrderedInfo.isPayByBalance()) {
                        //余额支付
                        llPaymentBalance.isVisible = true
                        tvPaymentBalance.text =
                            realPrice.priceFormatTwoDigitZero2()
                    } else {
                        llReceived.isVisible = true
                        tvReceivedAmount.text = realPrice.priceFormatTwoDigitZero2()
                        if (isCashPayment) {
                            tvReceived0.text = currentOrderedInfo.getOfflinePayMethodByLocal(
                                context,
                                localeList.first()
                            )

                            if (receiveAmount.isNotEmpty()) {
                                //如果是现金支付
                                tvReceivedAmount.text = receiveAmount
                            } else {
                                tvReceivedAmount.text = "$0.00"
                            }
                        } else {
                            tvReceived0.text = currentOrderedInfo.getPaymentMethodByLocal(
                                context,
                                localeList.first()
                            )
                            Timber.e("offlinePaymentChannelsNameEn  ${tvReceived0.text}")
                            tvReceivedAmount.text = realPrice.priceFormatTwoDigitZero2()
                        }
                    }

                    if (isCashPayment) {
                        //找零
                        llChange.isVisible = changeAmount.isNotEmpty()
                        tvChangeAmount.text = changeAmount
                    }
//                    if (tvReceived1.isVisible) {
//                        tvReceived1.text =
//                            "${tvReceived1.text} ${
//                                currentOrderedInfo?.getPaymentMethodByLocal(
//                                    context,
//                                    localeList.first()
//                                )
//                            }"
//                    } else {
//                        tvReceived0.text =
//                            "${tvReceived0.text} ${
//                                currentOrderedInfo?.getPaymentMethodByLocal(
//                                    context,
//                                    localeList.first()
//                                )
//                            }"
//                    }


                    //如果是收银小票
                    llPaidLayout.isVisible = currentOrderedInfo.isOrderSuccess()

                    /**
                     * 是否税务小票
                     */
                    if (isTaxAdministrationTicket || isOpenInvoiceInfo) {
                        if (isPreOrder && currentOrderedInfo.getDingTime().isNotEmpty()
                        ) {
                            llReserveTimeWithInvoice.isVisible = true
                            tvReserveTimeWithInvoice.text =
                                currentOrderedInfo.getDingTime()
                        }

                        tvPickUpNoWithInvoice.text = "${currentOrderedInfo.pickupCode}"
                        llPickUpNoWithInvoice.isVisible =
                            !currentOrderedInfo.pickupCode.isNullOrEmpty() && printTemplateResponseItem.informationShow?.showMealCode == true

                        tvTableNameWithInvoice.text = currentOrderedInfo.tableName
                        llTableNameWithInvoice.isVisible =
                            printTemplateResponseItem.informationShow?.showTableName == true

                        llOrderTypeWithInvoice.isVisible =
                            printTemplateResponseItem.informationShow?.showOrderType == true
                        tvOrderType.text = tvDiningStyle0.text

                        tvCompanyName.text = printTemplateResponseItem.companyName
                        llCompanyName.isVisible =
                            !tvCompanyName.text.isNullOrEmpty() && printTemplateResponseItem.informationShow?.showCompanyName == true

                        tvVatTin.text = printTemplateResponseItem.companyTaxNumber
                        llVatTin.isVisible =
                            !tvVatTin.text.isNullOrEmpty() && printTemplateResponseItem.informationShow?.showCompanyTaxNumber == true

                        tvCompanyAddress.text =
                            printTemplateResponseItem.companyAddress
                        llCompanyAddress.isVisible =
                            !tvCompanyAddress.text.isNullOrEmpty() && printTemplateResponseItem.informationShow?.showCompanyAddress == true

                        tvContactPhone.text =
                            printTemplateResponseItem.companyContactNumber
                        llContactPhone.isVisible =
                            !tvContactPhone.text.isNullOrEmpty() && printTemplateResponseItem.informationShow?.showCompanyContactNumber == true

                        tvContactEmail.text =
                            printTemplateResponseItem.companyContactEmail
                        llContactEmail.isVisible =
                            !tvContactEmail.text.isNullOrEmpty() && printTemplateResponseItem.informationShow?.showCompanyContactEmail == true

                        llCompanyInfo.isVisible = true
                        if (isTaxAdministrationTicket) {
                            //税务小票金额对应显示
                            tvTotalKhrPriceWithInvoice.text = "៛${khr.decimalFormatZeroDigit()}"
                            llKhrTotal.isVisible = true
                            llConversionRatio.isVisible = false
                        }
                    } else {
                        /**
                         * 普通小票
                         */

                        if (isPreOrder) {
                            llReserveTime.isVisible = true
                            tvReserveTime.text =
                                currentOrderedInfo.getDingTime()
                        }


                        llDiningStyle.isInvisible =
                            printTemplateResponseItem.informationShow?.showOrderType != true

                        llOrderType.isVisible =
                            llDiningStyle.isInvisible || tvPickUpNo.isVisible

                        tvPickUpNo.text = "${currentOrderedInfo.pickupCode}"
                        llPickUpNo.isVisible =
                            !currentOrderedInfo.pickupCode.isNullOrEmpty() && printTemplateResponseItem.informationShow?.showMealCode == true

                        tvTableName.text = currentOrderedInfo.tableName
                        llTableName.isVisible =
                            printTemplateResponseItem.informationShow?.showTableName == true

                    }


                    tvServiceLine.text = printTemplateResponseItem.servicePhone
                    llServiceLine.isVisible =
                        !tvServiceLine.text.isNullOrEmpty() && printTemplateResponseItem.informationShow?.showServicePhone == true

                    llOtherLayout.isVisible = llServiceLine.isVisible || llSource.isVisible


                    tvThankYouWords.isVisible =
                        printTemplateResponseItem.informationShow?.showSlogan == true && !printTemplateResponseItem.slogan.isNullOrEmpty()
                    tvThankYouWords.text = printTemplateResponseItem.slogan

                    llPassAppCoupon.isVisible =
                        printTemplateResponseItem.informationShow?.showPassAppPromotionCode == true && isCheckOut && printTemplateResponseItem.passAppPromotionCode?.isNotEmpty() == true
                    tvPassAppCoupon.text = printTemplateResponseItem.passAppPromotionCode

                    Timber.e("最后这里1111111  isPreCheckOut:${isPreCheckOut}")
                    //各种状态下线的矫正

                    if (isConfirmed) {
                        //确认单
                        lineSubtotal.isVisible =
                            !(!llPaymentQrCode.isVisible && !llPriceLayout.isVisible && !llPaidLayout.isVisible && !llPaidTotalLayout.isVisible && !llOtherLayout.isVisible && !tvThankYouWords.isVisible && !llPassAppCoupon.isVisible)
                        lineOther.isVisible = tvThankYouWords.isVisible
                    } else {
                        if (isCheckOut || isPreOrder) {
                            linePaid.isVisible =
                                llOtherLayout.isVisible || tvThankYouWords.isVisible || llPassAppCoupon.isVisible
                            Timber.e("llPassAppCoupon.isVisible: ${llPassAppCoupon.isVisible}   tvThankYouWords.isVisible:${tvThankYouWords.isVisible}")
                            lineOther.isVisible =
                                llPassAppCoupon.isVisible || tvThankYouWords.isVisible
                            linePassApp.isVisible = tvThankYouWords.isVisible
                        }
                        if (isPreCheckOut) {
                            lineTotalPrice.isVisible = false
                        }

                        lineSubtotal.isVisible = !llPriceLayout.isVisible
                    }

                    lineTop.isVisible =
                        ivLogo.isVisible || llCompanyInfo.isVisible || llStoreName.isVisible

                    /**
                     * 字号统一设置
                     */
                    if (printTemplateResponseItem.informationShow != null) {
                        tvPickUpNo.textSize =
                            printTemplateResponseItem.informationShow.getMealCodeFontRealSize(
                                context, isEightyWidth = isEightyWidth
                            )
                        tvPickUpNoWithInvoice.textSize =
                            printTemplateResponseItem.informationShow.getMealCodeFontRealSize(
                                context, isEightyWidth = isEightyWidth
                            )

                        tvTableName.textSize =
                            printTemplateResponseItem.informationShow.getTableNameFontRealSize(
                                context, isEightyWidth = isEightyWidth
                            )
                        tvTableNameWithInvoice.textSize =
                            printTemplateResponseItem.informationShow.getTableNameFontRealSize(
                                context, isEightyWidth = isEightyWidth
                            )

                        tvTotalUsdPrice.textSize =
                            printTemplateResponseItem.informationShow.getTotalAmountFontRealSize(
                                context, isEightyWidth = isEightyWidth
                            )
                        tvTotalKhrPrice.textSize =
                            printTemplateResponseItem.informationShow.getTotalAmountFontRealSize(
                                context, isEightyWidth = isEightyWidth
                            )
                        tvTotalVipPrice.textSize =
                            printTemplateResponseItem.informationShow.getTotalAmountFontRealSize(
                                context, isEightyWidth = isEightyWidth
                            )
                        tvTotalKhrPriceWithInvoice.textSize =
                            printTemplateResponseItem.informationShow.getTotalAmountFontRealSize(
                                context, isEightyWidth = isEightyWidth
                            )


                        tvChangeAmount.textSize =
                            printTemplateResponseItem.informationShow.getChangeAmountFontRealSize(
                                context, isEightyWidth = isEightyWidth
                            )
                    }

                    if (isEightyWidth == true) {
                        vFooter.thankYou.text =
                            context.getString(R.string.print_footer_thank_you_eigthy)
                    } else {
                        vFooter.thankYou.text = context.getString(R.string.print_footer_thank_you)
                        vFooter.root.setPadding(0, 0, 0, 80)
                    }


                    //380 58mm
                    //80mm 575
                    root.measure(
                        View.MeasureSpec.makeMeasureSpec(
                            width,
                            View.MeasureSpec.EXACTLY
                        ), View.MeasureSpec.makeMeasureSpec(
                            0,
                            View.MeasureSpec.UNSPECIFIED
                        )
                    )
                    root.layout(0, 0, root.measuredWidth, root.measuredHeight)


                    createBitmap =
                        createBitmap(
                            root.measuredWidth,
                            root.measuredHeight,
                            Bitmap.Config.ARGB_4444
                        )
                    val canvas = Canvas(createBitmap!!).apply {
                        drawColor(Color.WHITE)
                    }
                    root.draw(canvas)

//                    if (blackBitmap?.isRecycled == false) {
//                        blackBitmap.recycle()
//                    }
                }

            }
//            Timber.e("createBitmap  ${printerConfigInfo?.type}")
        } catch (e: Exception) {
            CrashReport.postCatchedException(e)
        }
        //方法一
//        return binding.root.drawingCache
        //方法二
        return createBitmap
    }


    fun setAllTextViewTextSize(rootView: View?, textSizeSp: Float) {
        if (rootView == null) {
            return
        }
        // 如果当前 View 是 TextView，直接设置字体大小
        if (rootView is TextView) {
            rootView.setTextSize(TypedValue.COMPLEX_UNIT_SP, textSizeSp)
            return
        }

        // 如果当前 View 是 ViewGroup，递归遍历其子 View
        if (rootView is ViewGroup) {
            for (i in 0 until rootView.childCount) {
                val child = rootView.getChildAt(i)
                setAllTextViewTextSize(child, textSizeSp)
            }
        }
    }

}