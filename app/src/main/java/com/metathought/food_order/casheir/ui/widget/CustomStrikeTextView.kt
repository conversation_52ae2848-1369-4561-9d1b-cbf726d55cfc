package com.metathought.food_order.casheir.ui.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView
import com.metathought.food_order.casheir.R

/**
 * ✅ 多行支持 - 现在每一行文本都会有独立的划线
 * ✅ 精确定位 - 每条划线都精确对应其所在行的文本
 * ✅ 空行处理 - 跳过空行，不为空行绘制划线
 * ✅ 位置可控 - 可以通过strikePosition控制划线在行内的垂直位置
 * ✅ 动态调整 - 可以在运行时动态修改划线属性
 */
class CustomStrikeTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatTextView(context, attrs, defStyleAttr) {

    private var strikePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
        color = currentTextColor
        strokeWidth = 2f // 默认宽度
    }

    // 中划线位置（0.0=顶部，1.0=底部）
    private var strikePosition = 0.5f

    init {
        // 禁用原生中划线
        paintFlags = paintFlags and Paint.STRIKE_THRU_TEXT_FLAG.inv()

        // 解析自定义属性
        context.obtainStyledAttributes(attrs, R.styleable.CustomStrikeTextView).apply {
            strikePaint.strokeWidth = getDimension(R.styleable.CustomStrikeTextView_strikeWidth, 2f)
            strikePaint.color =
                getColor(R.styleable.CustomStrikeTextView_strikeColor, currentTextColor)
            strikePosition = getFloat(R.styleable.CustomStrikeTextView_strikePosition, 0.5f)
            recycle()
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val text = text.toString()
        if (text.isEmpty()) return

        val layout = layout ?: return

        // 为每一行绘制划线
        for (lineIndex in 0 until layout.lineCount) {
            val lineStart = layout.getLineStart(lineIndex)
            val lineEnd = layout.getLineEnd(lineIndex)

            // 获取当前行的文本
            val lineText = text.substring(lineStart, lineEnd)
            if (lineText.trim().isEmpty()) continue

            // 获取行的位置信息
            val lineLeft = layout.getLineLeft(lineIndex)
            val lineRight = layout.getLineRight(lineIndex)
            val lineBaseline = layout.getLineBaseline(lineIndex)

            // 计算划线的Y位置
            val lineTop = layout.getLineTop(lineIndex)
            val lineBottom = layout.getLineBottom(lineIndex)
            val lineHeight = lineBottom - lineTop
            val strikeY = lineTop + lineHeight * strikePosition

            // 绘制当前行的划线
            canvas.drawLine(
                paddingLeft + lineLeft,
                paddingTop + strikeY,
                paddingLeft + lineRight,
                paddingTop + strikeY,
                strikePaint
            )
        }
    }

    /**
     * 设置划线宽度
     */
    fun setStrikeWidth(width: Float) {
        strikePaint.strokeWidth = width
        invalidate()
    }

    /**
     * 设置划线颜色
     */
    fun setStrikeColor(color: Int) {
        strikePaint.color = color
        invalidate()
    }

    /**
     * 设置划线位置（0.0=顶部，0.5=中间，1.0=底部）
     */
    fun setStrikePosition(position: Float) {
        strikePosition = position.coerceIn(0f, 1f)
        invalidate()
    }

    /**
     * 获取当前划线宽度
     */
    fun getStrikeWidth(): Float = strikePaint.strokeWidth

    /**
     * 获取当前划线颜色
     */
    fun getStrikeColor(): Int = strikePaint.color

    /**
     * 获取当前划线位置
     */
    fun getStrikePosition(): Float = strikePosition
}