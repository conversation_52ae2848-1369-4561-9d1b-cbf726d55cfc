package com.metathought.food_order.casheir.ui.dialog.store_report

import android.content.res.Resources
import android.icu.util.Calendar
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.FORMAT_DATE_TIME_SHOW
import com.metathought.food_order.casheir.constant.FORMAT_DATE_TIME_SHOW_WITH_SECOND
import com.metathought.food_order.casheir.constant.ReportDateType
import com.metathought.food_order.casheir.constant.ReportExportEnum
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.GoodClassificationModel
import com.metathought.food_order.casheir.data.model.base.response_model.report.ProductReportResponse
import com.metathought.food_order.casheir.databinding.DialogFilterGoodClassificationListBinding
import com.metathought.food_order.casheir.databinding.DialogProductReportBinding
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.formatDateStr
import com.metathought.food_order.casheir.extension.halfUp
import com.metathought.food_order.casheir.extension.hideKeyboard2
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.helper.FolderHelper
import com.metathought.food_order.casheir.helper.PermissionHelper
import com.metathought.food_order.casheir.helper.PopupWindowHelper
import com.metathought.food_order.casheir.helper.PreferenceHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.download.DownloadListener
import com.metathought.food_order.casheir.network.download.DownloadManager
import com.metathought.food_order.casheir.ui.adapter.FilterClassificationAdapter
import com.metathought.food_order.casheir.ui.adapter.ProductReportListAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.metathought.food_order.casheir.ui.dialog.print_report.PrintReportDialog
import com.metathought.food_order.casheir.ui.dialog.print_report.PrintReportViewModel
import com.metathought.food_order.casheir.ui.widget.printer.Printer
import com.metathought.food_order.casheir.utils.FileUtil
import com.metathought.food_order.casheir.utils.SingleClickUtils
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File
import java.math.BigDecimal
import java.util.Date


/**
 *<AUTHOR>
 *@time  2025/4/14
 *@desc 商品报表
 **/

@AndroidEntryPoint
class ProductReportDialog : BaseDialogFragment() {

    companion object {
        private const val TAG = "ProductReportDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance()
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? ProductReportDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
        ): ProductReportDialog {
            val args = Bundle()
            val fragment = ProductReportDialog()
            fragment.arguments = args
            return fragment
        }
    }

    private var binding: DialogProductReportBinding? = null
    private val viewModel: PrintReportViewModel by viewModels()

    private var responseData: ProductReportResponse? = null

    //时间类型
    private var dateType = ReportDateType.YESTERDAY.id
    private var startTime = Date()
    private var endTime = Date()

    //商品分组
    private var classificationList = mutableListOf<GoodClassificationModel>()

    //已选择的商品分组
    private var selectedClassificationItem = mutableListOf<GoodClassificationModel>()

    private var adapter = ProductReportListAdapter(ArrayList())

    private val searchRunnable = Runnable {
        try {
            binding?.apply {
                val list = tvCalendar.text.toString().split("-")
                if (list.size >= 2) {
                    viewModel.printProductReport(
                        requireContext(),
                        startTime.formatDateStr(FORMAT_DATE_TIME_SHOW_WITH_SECOND),
                        endTime.formatDateStr(FORMAT_DATE_TIME_SHOW_WITH_SECOND),
                        edtSearch.getSearchContent(),
                        selectedClassificationItem,
                        orderByColumn
                    )
                }
            }
        } catch (e: Exception) {

        }
    }

    private var orderByColumn = ""

    private fun postSearch(duration: Int = 700) {
        binding?.apply {
            edtSearch.removeCallbacks(searchRunnable)
            if (duration <= 0) {
                searchRunnable.run()
            } else {
                edtSearch.postDelayed(searchRunnable, duration.toLong())
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogProductReportBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        openKeyBoardListener()
        onTouchOutSide(binding?.root)

        initListener()
        initObserver()
        initView()
    }

    private fun initObserver() {
        viewModel.uiSaleItemsModel.observe(viewLifecycleOwner) { state ->
            when (state.response) {
                is ApiResponse.Loading -> {
                    binding?.apply {
                        pbLoading.isVisible = true
                    }
                }

                is ApiResponse.Success -> {
                    binding?.apply {
                        responseData = state.response.data
                        pbLoading.isVisible = false

                        initData()
                    }
                }

                is ApiResponse.Error -> {
                    binding?.apply {
                        pbLoading.isVisible = false
                    }
                }

                else -> {

                }

            }
        }
        viewModel.uiClassificationModel.observe(viewLifecycleOwner) { state ->
            if (state.response is ApiResponse.Success) {
                classificationList.addAll(state.response.data)
            }
        }
    }

    private fun initView() {
        binding?.apply {
            itemTotal.text = "${itemTotal.text}($)"
            tvServiceFee.text = "${tvServiceFee.text}($)"
            tvPackingFee.text = "${tvPackingFee.text}($)"
            tvActualReceivedTitle.text = "${tvActualReceivedTitle.text}($)"
//            tvOrderNumTitle.text = "${tvVatTitle.text}($)"

            tvActualReceivedTitle.text = tvActualReceivedTitle.text.toString().uppercase()
            tvTotalNumOfItemTitle.text = tvTotalNumOfItemTitle.text.toString().uppercase()
            tvOrderNumTitle.text = tvOrderNumTitle.text.toString().uppercase()

            rvList.adapter = adapter
            initDate()

            viewModel.getClassificationList()
            postSearch()
        }
    }

    private fun initDate() {
        binding?.apply {
            lifecycleScope.launch {
                val storeInfo = PreferenceHelper.getStoreInfo()
                val startCalendar = Calendar.getInstance()
                if (storeInfo?.isSetOpenStartTime() == true) {
                    //如果有设置营业时间,判断今天的营业时间时候已经到了
                    if (storeInfo.isCurrentInOpenTime()) {
                        dateType = ReportDateType.TODAY.id
                        Timber.e("今天的营业时间已经到了")
                        //营业时间-当前时间
                        val startTimeList = storeInfo.getStartTime()
                        endTime = (startCalendar.clone() as Calendar).time

                        //然后设置营业开始时间
                        startCalendar.set(Calendar.HOUR_OF_DAY, startTimeList[0])
                        startCalendar.set(Calendar.MINUTE, startTimeList[1])
                        startCalendar.set(Calendar.SECOND, startTimeList[2])
                        startCalendar.set(Calendar.MILLISECOND, 0)
                        startTime = (startCalendar.clone() as Calendar).time

                    } else {
                        dateType = ReportDateType.YESTERDAY.id
                        //今天的营业时间还没到就显示昨天的数据
                        Timber.e("今天的营业时间还没到就显示昨天的数据")
                        val startTimeList = storeInfo.getStartTime()
                        startCalendar.add(Calendar.DAY_OF_MONTH, -1) // 先将当前日期调整为昨天
                        //然后设置营业开始时间
                        startCalendar.set(Calendar.HOUR_OF_DAY, startTimeList[0])
                        startCalendar.set(Calendar.MINUTE, startTimeList[1])
                        startCalendar.set(Calendar.SECOND, startTimeList[2])
                        startCalendar.set(Calendar.MILLISECOND, 0)
                        startTime = (startCalendar.clone() as Calendar).time

                        // 先将当前日期调整为今天  的营业开始时间为结束时间
                        startCalendar.add(Calendar.DAY_OF_MONTH, +1)
                        startCalendar.set(Calendar.MILLISECOND, 0)
                        endTime = (startCalendar.clone() as Calendar).time
                    }
                } else {
                    //没设置营业 时间 显示昨天的数据
                    dateType = ReportDateType.YESTERDAY.id
                    startCalendar.add(Calendar.DAY_OF_MONTH, -1) // 先将当前日期调整为昨天
                    // 获取昨天开始时间（零点整）
                    startCalendar.set(Calendar.HOUR_OF_DAY, 0)
                    startCalendar.set(Calendar.MINUTE, 0)
                    startCalendar.set(Calendar.SECOND, 0)
                    startCalendar.set(Calendar.MILLISECOND, 0)
                    startTime = (startCalendar.clone() as Calendar).time

                    // 获取昨天结束时间（23点59分59秒）
                    startCalendar.set(Calendar.HOUR_OF_DAY, 23)
                    startCalendar.set(Calendar.MINUTE, 59)
                    startCalendar.set(Calendar.SECOND, 59)
                    startCalendar.set(Calendar.MILLISECOND, 999)
                    endTime = startCalendar.time
                }
            }

            tvCalendar.text = "${startTime.formatDateStr(FORMAT_DATE_TIME_SHOW)} - ${
                endTime.formatDateStr(FORMAT_DATE_TIME_SHOW)
            }"
        }
    }

    private fun initListener() {
        binding?.apply {
            topBar.getCloseBtn()?.setOnClickListener {
                dismissCurrentDialog()
            }

            tvCalendar.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    ReportDateSelectDialog.showDialog(
                        parentFragmentManager,
                        startTime,
                        endTime,
                        dateType
                    ) { startTime1, endTime1, dateType1 ->
                        startTime = startTime1
                        endTime = endTime1
                        tvCalendar.text = "${startTime.formatDateStr(FORMAT_DATE_TIME_SHOW)} - ${
                            endTime.formatDateStr(FORMAT_DATE_TIME_SHOW)
                        }"
                        dateType = dateType1

                        postSearch()
                    }
                }
            }

            dropdownFilter.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    if (isShowKeyBoard()) {
                        hideKeyboard2()
                    } else {
                        initPopupTableView(dropdownFilter)
                    }
                }
            }

            edtSearch.setTextChangedListenerCallBack {
                postSearch()
            }

            tvClearFilter.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    reset()
                }
            }

            btnPrint.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    if (responseData == null || responseData?.data?.salesItemOrdersDetailList.isNullOrEmpty()) {
                        showToast(requireContext().getString(R.string.empty_data))
                        return@isFastDoubleClick
                    }
                    Printer.printPrinterProductReport(
                        requireContext(),
                        responseData
                    )
                }
            }


            btnExport.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    if (responseData == null || responseData?.data?.salesItemOrdersDetailList.isNullOrEmpty()) {
                        showToast(requireContext().getString(R.string.empty_data))
                        return@isFastDoubleClick
                    }
                    if (PermissionHelper.checkPermissions(requireActivity())) {
                        SelectExportTypeDialog.showDialog(parentFragmentManager) {
                            btnExport.setEnableWithAlpha(false)
                            viewModel.export(
                                requireContext(),
                                startTime.formatDateStr(FORMAT_DATE_TIME_SHOW_WITH_SECOND),
                                endTime.formatDateStr(FORMAT_DATE_TIME_SHOW_WITH_SECOND),
                                ReportExportEnum.PRODUCT_REPORT.id,
                                it,
                                edtSearch.getSearchContent(),
                                selectedClassificationItem,
                                orderByColumn
                            ) { url ->
                                if (url != null) {
                                    download(url)
                                } else {
                                    btnExport.setEnableWithAlpha(true)
                                }
                            }
                        }
                    } else {
                        PermissionHelper.requestPermissions(requireActivity())
                    }
                }
            }
            val sortNormal = ContextCompat.getDrawable(
                requireContext(),
                R.drawable.icon_sort_normal
            )
            val descNormal = ContextCompat.getDrawable(
                requireContext(),
                R.drawable.icon_sort_top
            )
            val ascNormal = ContextCompat.getDrawable(
                requireContext(),
                R.drawable.icon_sort_down
            )
            //desc是降序
            itemName.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    val tmp = orderByColumn.split(",")
                    if (tmp.size < 2) {
                        //如果 没排序过
                        orderByColumn = "itemName,desc"
                        itemName.setCompoundDrawablesWithIntrinsicBounds(
                            null,
                            null,
                            descNormal,
                            null
                        )
                    } else {
                        if (tmp[0] != "itemName") {
                            //如果有排序过 且不是当前 排序的列 先降序
                            orderByColumn = "itemName,desc"
                            itemName.setCompoundDrawablesWithIntrinsicBounds(
                                null,
                                null,
                                descNormal,
                                null
                            )
                        } else {
                            if (tmp[1] == "desc") {
                                //原来是降序 改成升序
                                orderByColumn = "itemName,asc"
                                itemName.setCompoundDrawablesWithIntrinsicBounds(
                                    null,
                                    null,
                                    ascNormal,
                                    null
                                )
                            } else {
                                //原来是升序 改成降序
                                orderByColumn = "itemName,desc"
                                itemName.setCompoundDrawablesWithIntrinsicBounds(
                                    null,
                                    null,
                                    descNormal,
                                    null
                                )
                            }
                        }
                    }
                    itemQty.setCompoundDrawablesWithIntrinsicBounds(null, null, sortNormal, null)
                    itemTotal.setCompoundDrawablesWithIntrinsicBounds(null, null, sortNormal, null)
                    postSearch()
                }
            }

            itemQty.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    val tmp = orderByColumn.split(",")
                    if (tmp.size < 2) {
                        //如果 没排序过
                        orderByColumn = "itemNum,desc"
                        itemQty.setCompoundDrawablesWithIntrinsicBounds(
                            null,
                            null,
                            descNormal,
                            null
                        )
                    } else {
                        if (tmp[0] != "itemNum") {
                            //如果有排序过 且不是当前 排序的列 先降序
                            orderByColumn = "itemNum,desc"
                            itemQty.setCompoundDrawablesWithIntrinsicBounds(
                                null,
                                null,
                                descNormal,
                                null
                            )
                        } else {
                            if (tmp[1] == "desc") {
                                //原来是降序 改成升序
                                orderByColumn = "itemNum,asc"
                                itemQty.setCompoundDrawablesWithIntrinsicBounds(
                                    null,
                                    null,
                                    ascNormal,
                                    null
                                )
                            } else {
                                //原来是升序 改成降序
                                orderByColumn = "itemNum,desc"
                                itemQty.setCompoundDrawablesWithIntrinsicBounds(
                                    null,
                                    null,
                                    descNormal,
                                    null
                                )
                            }
                        }
                    }
                    itemName.setCompoundDrawablesWithIntrinsicBounds(null, null, sortNormal, null)
                    itemTotal.setCompoundDrawablesWithIntrinsicBounds(null, null, sortNormal, null)
                    postSearch()
                }
            }
            itemTotal.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    val tmp = orderByColumn.split(",")
                    if (tmp.size < 2) {
                        //如果 没排序过
                        orderByColumn = "itemTotalPrice,desc"
                        itemTotal.setCompoundDrawablesWithIntrinsicBounds(
                            null,
                            null,
                            descNormal,
                            null
                        )
                    } else {
                        if (tmp[0] != "itemTotalPrice") {
                            //如果有排序过 且不是当前 排序的列 先降序
                            orderByColumn = "itemTotalPrice,desc"
                            itemTotal.setCompoundDrawablesWithIntrinsicBounds(
                                null,
                                null,
                                descNormal,
                                null
                            )
                        } else {
                            if (tmp[1] == "desc") {
                                //原来是降序 改成升序
                                orderByColumn = "itemTotalPrice,asc"
                                itemTotal.setCompoundDrawablesWithIntrinsicBounds(
                                    null,
                                    null,
                                    ascNormal,
                                    null
                                )
                            } else {
                                //原来是升序 改成降序
                                orderByColumn = "itemTotalPrice,desc"
                                itemTotal.setCompoundDrawablesWithIntrinsicBounds(
                                    null,
                                    null,
                                    descNormal,
                                    null
                                )
                            }
                        }
                    }
                    itemName.setCompoundDrawablesWithIntrinsicBounds(null, null, sortNormal, null)
                    itemQty.setCompoundDrawablesWithIntrinsicBounds(null, null, sortNormal, null)
                    postSearch()
                }
            }
        }
    }


    private fun initData() {
        binding?.apply {
            tvActualReceived.text = (responseData?.data?.totalReceiveAmount?.toBigDecimalOrNull()
                ?: BigDecimal.ZERO).halfUp(2).decimalFormatTwoDigitZero()
            tvTotalNum.text = "${responseData?.data?.totalItemNum ?: 0}"
            tvOrderNum.text = "${responseData?.data?.totalOrderNum ?: 0}"


            if (responseData?.data?.salesItemOrdersDetailList.isNullOrEmpty()) {
                llBottom.isVisible = false
                layoutEmpty.root.isVisible = true
                rvList.isVisible = false
            } else {
                llBottom.isVisible = true
                layoutEmpty.root.isVisible = false
                rvList.isVisible = true
                adapter.replaceData(
                    ArrayList(
                        responseData?.data?.salesItemOrdersDetailList ?: listOf()
                    )
                )
            }

        }
    }

    private fun reset() {
        binding?.apply {
            edtSearch.setSearchContent("")
            initDate()
            selectedClassificationItem.clear()
            binding?.tvType?.text = getFilterClassificationStr()
            postSearch()
        }
    }

    private fun initPopupTableView(anchorView: View) {
        val popupViewTable = DialogFilterGoodClassificationListBinding.inflate(layoutInflater)
        val popupWindow = PopupWindow(
            popupViewTable?.root,
            (Resources.getSystem().displayMetrics.widthPixels * 0.3).toInt(),
            (Resources.getSystem().displayMetrics.heightPixels * 0.5).toInt(),
            true
        )
        binding?.arrow?.animate()?.rotation(180f)?.setDuration(200)
        popupWindow?.elevation = 20f
        popupWindow?.animationStyle = R.style.PopupAnimation
        popupWindow.showAsDropDown(anchorView)
        popupWindow.setOnDismissListener {
            PopupWindowHelper.deletePopupWindow(popupWindow)
            binding?.arrow?.animate()?.rotation(0f)?.setDuration(200)
            anchorView.setBackgroundResource(R.drawable.background_language_spiner)
        }

        popupViewTable?.apply {
            val filterTableAdapter =
                context?.let {
                    FilterClassificationAdapter(
                        ArrayList(classificationList.map { it.copy(ischeck = false) }),
                        it,
                        selectedClassificationItem
                    )
                }
            recyclerViewTable.adapter = filterTableAdapter
            btnConfirm.setOnClickListener {
                selectedClassificationItem.clear()
                selectedClassificationItem.addAll(
                    filterTableAdapter?.getSelectedArrayList() ?: listOf()
                )
                binding?.tvType?.text = getFilterClassificationStr()
                postSearch()
                popupWindow?.dismiss()

            }

            btnCancel.setOnClickListener {
                filterTableAdapter?.resetSelect()
            }
        }
    }

    /**
     * 获取过滤商品分类字符串
     *
     */
    private fun getFilterClassificationStr(): String {
//        var str = ""
//        selectedClassificationItem.forEachIndexed { index, goodClassificationModel ->
//            if (str.isNullOrEmpty()) {
//                str = goodClassificationModel.groupName ?: ""
//            } else {
//                str = "、${goodClassificationModel.groupName ?: ""}"
//            }
//        }
//        var str =  selectedClassificationItem.map { it.groupName?:"" }.joinToString("、")
        var str = selectedClassificationItem.joinToString(
            separator = "、",
        ) { it.groupName ?: "" }
        if (str.isNullOrEmpty()) {
            str = getString(R.string.product_category)
        }
        return str
    }

    private fun download(url: String) {
        val fileName = FileUtil.getFileNameInUrl(url)
        val dir = FolderHelper.getDownloadFolderPath()
        DownloadManager.getInstance()
            .download(
                TAG,
                url,
                fileName,
                dir ?: "",
                object : DownloadListener {
                    override fun onProgress(progress: Long, max: Long) {
                        requireActivity().apply {
                            Timber.e("progress $progress")
                            runOnUiThread {
                                binding?.apply {
                                    val currentProgress =
                                        ((progress * 1.0f / max).times(100)).toInt()
                                }
                            }
                        }
                    }

                    override fun onSuccess(localPath: String) {
                        val file = File(dir, fileName)
                        val renameToRes = File(localPath).renameTo(file)
                        FileUtil.scanFile(requireActivity(), file)
                        requireActivity().runOnUiThread {
                            binding?.apply {
                                btnExport.setEnableWithAlpha(true)
                            }
                            Toast.makeText(
                                requireActivity(),
                                requireActivity().getString(
                                    R.string.save_at_location,
                                    file.absolutePath
                                ),
                                Toast.LENGTH_LONG
                            ).show()
                        }

                    }

                    override fun onFail(errorInfo: String) {
                        requireActivity().apply {
                            runOnUiThread {
                                binding?.apply {
                                    btnExport.setEnableWithAlpha(true)
                                }
                                if (errorInfo.isNotEmpty())
                                    Toast.makeText(context, errorInfo, Toast.LENGTH_LONG).show()
                            }
                        }
                    }
                })
    }


    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.85).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.95).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }
}