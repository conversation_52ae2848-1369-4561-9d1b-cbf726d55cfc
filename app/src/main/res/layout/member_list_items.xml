<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="60dp"
    android:orientation="horizontal">

    <!-- 左侧固定：客户昵称列 -->
<!--    <TextView-->
<!--        android:id="@+id/tvAccountName"-->
<!--        style="@style/FontLocalization"-->
<!--        android:layout_width="140dp"-->
<!--        android:layout_height="match_parent"-->
<!--        android:ellipsize="end"-->
<!--        android:gravity="center_vertical"-->
<!--        android:maxLines="2"-->
<!--        android:paddingEnd="10dp"-->
<!--        android:singleLine="true"-->
<!--        android:textColor="@color/black"-->
<!--        android:textSize="16sp"-->
<!--        tools:text="林超正超正常的..." />-->

    <!-- 中间可滑动部分 -->
    <HorizontalScrollView
        android:id="@+id/itemScrollView"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:fadeScrollbars="false"
        android:overScrollMode="never"
        android:scrollbarStyle="outsideOverlay"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvMemberNumber"
                style="@style/FontLocalization"
                android:layout_width="140dp"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="2"
                android:paddingEnd="10dp"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textSize="16sp"
                tools:text="M001" />

            <TextView
                android:id="@+id/tvAccountNumber"
                style="@style/FontLocalization"
                android:layout_width="140dp"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="2"
                android:paddingEnd="10dp"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textSize="16sp"
                tools:text="***********" />

            <TextView
                android:id="@+id/tvAmount"
                style="@style/FontLocalization"
                android:layout_width="120dp"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="2"
                android:paddingEnd="10dp"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textSize="16sp"
                tools:text="$ 9.9" />

            <TextView
                android:id="@+id/tvRegisterDate"
                style="@style/FontLocalization"
                android:layout_width="190dp"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="2"
                android:paddingEnd="10dp"
                android:textColor="@color/black"
                android:textSize="16sp"
                tools:text="2024/02/02  12:00:00" />

            <TextView
                android:id="@+id/tvLastTopUpDate"
                style="@style/FontLocalization"
                android:layout_width="190dp"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="2"
                android:paddingEnd="10dp"
                android:textColor="@color/black"
                android:textSize="16sp"
                tools:text="2024/02/02  12:00:00" />

            <TextView
                android:id="@+id/tvRechargeTimes"
                style="@style/FontLocalization"
                android:layout_width="120dp"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="2"
                android:paddingEnd="10dp"
                android:textColor="@color/black"
                android:textSize="16sp"
                tools:text="1000" />

            <TextView
                android:id="@+id/tvConsumptionTimes"
                style="@style/FontLocalization"
                android:layout_width="120dp"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="2"
                android:paddingEnd="10dp"
                android:textColor="@color/black"
                android:textSize="16sp"
                tools:text="1000" />

        </LinearLayout>
    </HorizontalScrollView>


</LinearLayout>