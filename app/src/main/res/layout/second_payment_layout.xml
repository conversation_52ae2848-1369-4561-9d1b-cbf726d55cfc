<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/second_payment_bg"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clPaymentLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingHorizontal="80dp"
        android:paddingVertical="40dp">

        <LinearLayout
            android:id="@+id/layoutQR"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:clipToPadding="false"
            android:padding="1dp"
            android:background="@drawable/rectangle"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.4">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:background="@drawable/top_khqr"
                android:padding="6dp"
                android:src="@drawable/ic_khqr_logo" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="90dp"
                android:background="@drawable/info_khqr"
                android:gravity="center_vertical"
                android:orientation="vertical"
                android:paddingLeft="30dp"
                android:paddingTop="8dp"
                android:paddingBottom="8dp">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvScanQRName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:lines="1"
                    android:textColor="@color/black"
                    android:textSize="20sp"
                    android:visibility="visible"
                    app:autoSizeTextType="uniform"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    tools:text="KUNTHEA SOT" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvScanQRAmount"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:lines="1"
                    android:textColor="@color/black"
                    android:textSize="25sp"
                    android:textStyle="bold"
                    app:autoSizeTextType="uniform"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    tools:text="$673.57" />


            </LinearLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bottom_khqr"
                android:padding="30dp">

                <ImageView
                    android:id="@+id/imgQR"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:adjustViewBounds="true"
                    android:src="@drawable/ic_qr_download" />
            </RelativeLayout>

        </LinearLayout>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvInfo"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginTop="40dp"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:lines="1"
            android:text="@string/plz_scan_qr_pay_before_expires"
            android:textColor="@color/black"
            android:textSize="30sp"
            android:textStyle="bold"
            app:autoSizeTextType="uniform"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/layoutQR"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDuration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginTop="10dp"
            android:background="@drawable/background_language_spiner"
            android:lines="1"
            android:paddingHorizontal="30dp"
            android:paddingVertical="10dp"
            android:textColor="@color/khqr_red_color"
            android:textSize="30sp"
            android:textStyle="bold"
            app:autoSizeTextType="uniform"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/layoutQR"
            app:layout_constraintTop_toBottomOf="@id/tvInfo"
            tools:text="04:11" />

        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_centerInParent="true"
            android:adjustViewBounds="true"
            android:padding="20dp"
            android:src="@mipmap/ic_second_payment_icon"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/layoutQR"
            app:layout_constraintTop_toBottomOf="@id/tvDuration" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clPaymentResultLayout"
        android:layout_width="match_parent"
        android:paddingHorizontal="60dp"
        android:paddingVertical="40dp"
        tools:visibility="gone"
        android:layout_height="match_parent">

        <FrameLayout
            android:id="@+id/printTicketLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.55">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:background="@drawable/shape_payment_result_rounded_rectangle" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="15dp"
                android:paddingVertical="30dp"
                android:layout_marginTop="15dp"
                android:paddingHorizontal="20dp"
                android:background="@drawable/shape_payment_result_rounded_rectangle_white_bg"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/tableName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/table_name"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_default_sp" />

                    <TextView
                        android:id="@+id/tvTableName"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="10dp"
                        android:gravity="end"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_default_sp"
                        tools:text="A001" />
                </LinearLayout>

                <LinearLayout
                    android:layout_marginBottom="10dp"
                    android:layout_marginTop="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/numberProducts"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/number_products"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_default_sp" />

                    <TextView
                        android:id="@+id/tvFoodCount"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_default_sp"
                        tools:text="10" />
                </LinearLayout>

                <include layout="@layout/dot_divider"/>


                <LinearLayout
                    android:layout_marginTop="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/subtotal"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/subtotal"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_default_sp" />

                    <TextView
                        android:id="@+id/tvSubtotal"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_default_sp"
                        tools:text="$100" />
                </LinearLayout>

                <LinearLayout
                    android:layout_marginTop="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/vat"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/vat"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_default_sp" />

                    <TextView
                        android:id="@+id/tvVat"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_default_sp"
                        tools:text="$2" />
                </LinearLayout>

                <LinearLayout
                    android:layout_marginTop="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/discount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/discount_reduction"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_default_sp" />

                    <TextView
                        android:id="@+id/tvDiscountReduction"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_default_sp"
                        tools:text="$2" />
                </LinearLayout>

                <include layout="@layout/dot_divider"/>

                <LinearLayout
                    android:layout_marginTop="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/receivable"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/receivable"
                        android:layout_gravity="center_vertical"
                        android:textColor="@color/paid_text_color"
                        android:textSize="@dimen/_default_sp" />

                    <TextView
                        android:id="@+id/tvReceivable"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="end"
                        android:textColor="@color/paid_text_color"
                        android:textSize="50sp"
                        tools:text="$92" />
                </LinearLayout>

            </LinearLayout>


        </FrameLayout>

        <LinearLayout
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/printTicketLayout"
            app:layout_constraintStart_toEndOf="@id/printTicketLayout"
            app:layout_constraintBottom_toBottomOf="@id/printTicketLayout"
            android:orientation="vertical"
            android:layout_marginTop="20dp"
            android:gravity="center_horizontal"
            android:layout_width="0dp"
            android:layout_height="0dp">
            <ImageView
                android:src="@drawable/ic_second_payment_done"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <TextView
                android:id="@+id/paymentResult"
                android:layout_marginTop="20dp"
                android:textSize="40sp"
                android:gravity="center_horizontal"
                android:textColor="@color/paid_text_color"
                android:text="@string/payment_successfully"
                android:textStyle="bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>


            <LinearLayout
                android:layout_weight="1"
                android:gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="0dp">
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <TextView
                        android:id="@+id/totalPrice"
                        android:text="$"
                        app:layout_constraintStart_toStartOf="parent"
                        android:textColor="@color/black"
                        android:textSize="30sp"
                        android:textStyle="bold"
                        app:layout_constraintBaseline_toBaselineOf="@id/tvTotalPrice"
                        android:layout_gravity="bottom"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                    <TextView
                        android:id="@+id/tvTotalPrice"
                        tools:text="92.00"
                        android:textSize="50sp"
                        app:layout_constraintStart_toEndOf="@id/totalPrice"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        android:layout_marginStart="10dp"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"/>
                </androidx.constraintlayout.widget.ConstraintLayout>
            </LinearLayout>
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>