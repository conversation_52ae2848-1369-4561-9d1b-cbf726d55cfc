<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="@drawable/background_popup_dropdown"
    android:padding="10dp"
    android:gravity="center_horizontal">

    <TextView
        android:id="@+id/tvEnglish"
        android:gravity="center"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:fontFamily="@font/roboto"
        android:textColor="@color/black"
        android:text="@string/english_language"/>
    <TextView
        android:id="@+id/tvKhmer"
        android:gravity="center"
        android:fontFamily="@font/khmer_os_battambang_regular"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:textColor="@color/black"
        android:text="@string/khmer_language"/>
    <TextView
        android:id="@+id/tvChinese"
        android:gravity="center"
        android:textColor="@color/black"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:text="@string/chinese_language"/>

</LinearLayout>