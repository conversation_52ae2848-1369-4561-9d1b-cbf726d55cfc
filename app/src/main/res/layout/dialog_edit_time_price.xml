<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutMain"
        android:layout_width="520dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:padding="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:dialog_title="@string/set_time_price"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ScrollView
            android:id="@+id/scrollView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:background="@drawable/background_efefef_radius_12"
            android:paddingHorizontal="10dp"
            android:paddingVertical="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_max="300dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/topBar">

            <LinearLayout
                android:id="@+id/llGoodInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/layoutContent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="top"
                        android:layout_marginEnd="2dp"
                        android:layout_weight="1.8"
                        android:orientation="vertical"
                        android:text="@string/items"
                        android:textColor="@color/black50"
                        android:textSize="@dimen/_14ssp">

                        <TextView
                            android:id="@+id/tvName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:ellipsize="end"
                            android:maxLines="2"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp"
                            tools:text="Lemonade" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tvTmpSign"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="5dp"
                                android:ellipsize="end"
                                android:maxLines="1"
                                android:paddingHorizontal="4dp"
                                android:paddingVertical="2dp"
                                android:text="@string/temporary"
                                android:textColor="@color/black60"
                                android:textSize="@dimen/_11ssp"
                                android:visibility="gone"
                                tools:visibility="visible" />

                            <TextView
                                android:id="@+id/tvDiscountActivity"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:ellipsize="end"
                                android:maxLines="1"
                                android:paddingHorizontal="4dp"
                                android:paddingVertical="2dp"
                                android:textColor="@color/primaryColor"
                                android:textSize="@dimen/_11ssp"
                                android:visibility="gone"
                                tools:text="第二份半价"
                                tools:visibility="visible" />

                        </LinearLayout>


                        <TextView
                            android:id="@+id/tvSpecification"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end"
                            android:ellipsize="end"
                            android:textColor="@color/black60"
                            android:textSize="@dimen/_12ssp"
                            android:visibility="gone"
                            tools:text="Large Normal ice, Normal sugar"
                            tools:visibility="visible" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="top"
                        android:layout_marginEnd="2dp"
                        android:layout_weight="1"
                        android:gravity="end">

                        <ImageView
                            android:id="@+id/imgMinus"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_minus"
                            android:visibility="gone"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/tvQTY"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:maxLength="4"
                            android:paddingHorizontal="10dp"
                            android:text="0"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16ssp"
                            tools:text="0" />

                        <ImageView
                            android:id="@+id/imgPlus"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_add"
                            android:visibility="gone"
                            tools:visibility="visible" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llWeight"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1"
                        android:text="@string/weight"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp" />

                    <TextView
                        android:id="@+id/tvWeight"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:ellipsize="end"
                        android:maxLines="2"
                        android:text="@string/to_be_weighed"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="1"
                        android:text="@string/total_price2"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp" />

                    <TextView
                        android:id="@+id/tvPrice"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:ellipsize="end"
                        android:maxLines="2"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_16ssp"
                        tools:text="时价" />

                    <TextView
                        android:id="@+id/tvVipPrice"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="1dp"
                        android:drawablePadding="2dp"
                        android:textColor="@color/member_price_color"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        app:drawableStartCompat="@drawable/icon_vip"
                        tools:text="$0.00"
                        tools:visibility="visible" />
                </LinearLayout>

            </LinearLayout>

        </ScrollView>


        <LinearLayout
            android:id="@+id/llInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/scrollView">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutUsd"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:hint="@string/product_sale_price2"
                    android:orientation="horizontal"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:startIconDrawable="@drawable/icon_dollor_black"
                    app:startIconTint="@color/black">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtUsd"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:inputType="numberDecimal"
                        android:maxLines="1"
                        android:paddingVertical="8dp"
                        android:paddingStart="35dp"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black40"
                        android:textSize="@dimen/_14ssp" />

                </com.google.android.material.textfield.TextInputLayout>

                <TextView
                    android:id="@+id/tvWeightUnit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:layout_marginEnd="14dp"
                    android:textSize="@dimen/_16ssp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:visibility="gone"
                    tools:visibility="visible"
                    tools:text="/kg" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_weight="1">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutVipUsd"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:hint="@string/vip_price"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:startIconDrawable="@drawable/icon_dollor_black"
                    app:startIconTint="@color/black">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtVipUsd"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:inputType="numberDecimal"
                        android:maxLines="1"
                        android:paddingVertical="8dp"
                        android:paddingStart="35dp"
                        android:singleLine="true"
                        android:textColor="@color/black"
                        android:textColorHint="@color/black40"
                        android:textSize="@dimen/_14ssp" />

                </com.google.android.material.textfield.TextInputLayout>

                <TextView
                    android:id="@+id/tvVipWeightUnit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:layout_marginEnd="14dp"
                    android:textSize="@dimen/_16ssp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:visibility="gone"
                    tools:visibility="visible"
                    tools:text="/kg" />

            </androidx.constraintlayout.widget.ConstraintLayout>


        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="45dp"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/llInput">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnCancel"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_marginEnd="16dp"
                android:layout_weight="1"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:text="@string/cancel"
                android:textAllCaps="false"
                android:textColor="@color/black"
                android:textSize="@dimen/_18ssp"
                android:textStyle="bold"
                app:backgroundTint="@color/transparent"
                app:cornerRadius="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:strokeColor="@color/black20"
                app:strokeWidth="1dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnYes"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:text="@string/confirm2"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/_18ssp"
                android:textStyle="bold"
                app:backgroundTint="@color/primaryColor"
                app:cornerRadius="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:strokeColor="@color/primaryColor"
                app:strokeWidth="0dp" />

        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
