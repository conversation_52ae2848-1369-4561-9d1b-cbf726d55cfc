<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.google.android.material.card.MaterialCardView
        style="@style/CustomCardViewStyle"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="3dp"
        android:layout_marginBottom="3dp"
        android:layout_marginEnd="1dp"
        android:clipToPadding="true"
        android:orientation="vertical"
        app:cardBackgroundColor="@color/white"
        app:cardElevation="3dp"
        app:strokeWidth="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewPage"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_gravity="start"
                android:layout_margin="10dp"
                android:orientation="horizontal"
                android:overScrollMode="never"
                android:scrollbars="none"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="8"
                tools:listitem="@layout/floor_item" />

            <View
                style="@style/commonDividerStyle"
                android:background="@color/black12" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerViewTable"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:clipToPadding="false"
                    android:overScrollMode="never"
                    android:scrollbars="none"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:spanCount="2"
                    tools:itemCount="6"
                    tools:listitem="@layout/filter_table_item" />

                <ProgressBar
                    android:id="@+id/progressBar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_gravity="center"
                    android:visibility="gone" />
            </RelativeLayout>

            <View
                style="@style/commonDividerStyle"
                android:background="@color/black12" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingVertical="10dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnCancel"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="wrap_content"
                    android:layout_height="60dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="10dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:text="@string/reset"
                    android:textAllCaps="false"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_18ssp"
                    app:backgroundTint="@color/white"
                    app:cornerRadius="15dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:strokeColor="@color/black20"
                    app:strokeWidth="1dp" />


                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnConfirm"
                    style="@style/CustomOutlinedBox"
                    android:layout_width="wrap_content"
                    android:layout_height="60dp"
                    android:layout_gravity="center_vertical"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:text="@string/apply_filter"
                    android:textAllCaps="false"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_18ssp"
                    app:backgroundTint="@color/primaryColor"
                    app:cornerRadius="15dp"
                    app:layout_constraintEnd_toEndOf="parent" />
            </LinearLayout>
            <!--    </LinearLayout>-->

        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

</FrameLayout>