<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:gravity="center"
    android:layout_height="wrap_content">
    <ImageView
        android:id="@+id/imgError"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_empty_box"/>
    <TextView
        android:id="@+id/tvEmptyText"
        android:layout_marginTop="10dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/black80"
        android:text="@string/empty_data"
        style="@style/FontLocalization"/>
</LinearLayout>