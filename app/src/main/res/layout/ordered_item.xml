<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <!--    <com.google.android.material.card.MaterialCardView-->
    <!--        android:id="@+id/cardViewMain"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="match_parent"-->
    <!--        android:layout_marginTop="10dp"-->
    <!--        android:layout_marginEnd="10dp"-->
    <!--        android:clipToPadding="false"-->
    <!--        android:elevation="0dp"-->
    <!--        android:paddingTop="10dp"-->
    <!--        app:cardBackgroundColor="@color/white"-->
    <!--        app:cardCornerRadius="12dp"-->
    <!--        app:cardElevation="0dp"-->
    <!--        app:strokeColor="@color/primaryColor"-->
    <!--        app:strokeWidth="2dp">-->

    <!--        <FrameLayout-->
    <!--            android:layout_width="match_parent"-->
    <!--            android:layout_height="match_parent">-->

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clContent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingHorizontal="12dp"
        android:paddingVertical="16dp">

        <TextView
            android:id="@+id/tvOrderType"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:drawablePadding="2dp"
            android:text="@string/dine_in"
            android:textColor="@color/black"
            android:textStyle="bold"
            app:drawableStartCompat="@drawable/icon_dine_in"
            app:layout_constraintEnd_toStartOf="@id/ivPrint"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/tvCountDownTime"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:maxLines="1"
            android:textColor="@color/color_ff3141"
            android:textSize="@dimen/_16ssp"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/statusCardView"
            app:layout_constraintEnd_toStartOf="@id/ivPrint"
            app:layout_constraintTop_toTopOf="@id/statusCardView"
            tools:text="倒计时 09:00"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/ivPrint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:src="@drawable/icon_unprint"
            app:layout_constraintBottom_toBottomOf="@id/statusCardView"
            app:layout_constraintEnd_toStartOf="@id/statusCardView"
            app:layout_constraintTop_toTopOf="@id/statusCardView" />


        <androidx.cardview.widget.CardView
            android:id="@+id/statusCardView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:elevation="0dp"
            app:cardBackgroundColor="@color/confirm_backgroud_color"
            app:cardCornerRadius="8dp"
            app:cardElevation="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintStart_toEndOf="@id/tvOrderType"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tvStatus"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="25dp"
                android:gravity="center_vertical|end"
                android:maxLines="1"
                android:paddingHorizontal="8dp"
                android:paddingVertical="3dp"
                android:text="@string/confirmed"
                android:textColor="@color/confirm_text_color"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold" />
        </androidx.cardview.widget.CardView>

        <LinearLayout
            android:id="@+id/llTableName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvOrderType">

            <TextView
                android:id="@+id/tvTableID"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:lines="1"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textStyle="bold"
                tools:text="@string/login_time" />

            <TextView
                android:id="@+id/tvPickUpNo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/print_title_pick_up_no"
                android:textColor="@color/primaryColor"
                android:textSize="@dimen/_16ssp"
                android:textStyle="bold"
                android:visibility="gone"
                tools:text="Pick-up No. : P001"
                tools:visibility="visible" />
        </LinearLayout>


        <TextView
            android:id="@+id/tvOrderedID"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="start"
            android:maxLines="1"
            android:text="@string/order_id"
            android:textColor="@color/black80"
            android:textSize="@dimen/_14ssp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/llTableName" />

        <TextView
            android:id="@+id/tvTime"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="start"
            android:maxLines="1"
            android:text="@string/ordering_time"
            android:textColor="@color/black80"
            android:textSize="@dimen/_14ssp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvOrderedID" />

        <TextView
            android:id="@+id/tvPrice"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:drawablePadding="0dp"
            android:gravity="bottom|end"
            android:maxLines="1"
            android:textColor="@color/black"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="$99.99" />

        <TextView
            android:id="@+id/tvItems"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="start"
            android:maxLines="1"
            android:text="@string/items"
            android:textColor="@color/black80"
            android:textSize="@dimen/_14ssp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTime" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/ivNew"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/icon_new_order"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />
    <!--        </FrameLayout>-->


    <!--    </com.google.android.material.card.MaterialCardView>-->

    <ImageView
        android:id="@+id/ivPrintPreSettlement"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end|top"
        android:src="@drawable/icon_print_pre_settlement"
        android:visibility="gone"
        tools:visibility="visible" />

    <View
        style="@style/commonDividerStyle"
        android:layout_gravity="bottom"
        android:layout_marginHorizontal="12dp" />
</FrameLayout>
