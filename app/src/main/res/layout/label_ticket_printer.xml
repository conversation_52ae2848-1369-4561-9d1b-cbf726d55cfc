<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingTop="5px"
    android:paddingBottom="8px">

    <TextView
        android:id="@+id/tvStoreName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="1"
        android:paddingEnd="10px"
        android:textColor="@color/black"
        android:textSize="16px"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="សម្រាកបណ្តោះអាសន្នសម្រាកបណ្តោះអាសន្សម្រាកបណ្តោះអាសន្នសម្រាកបណ្តោះអាសន្នន"
        tools:visibility="visible" />

    <!-- 中间内容区域使用LinearLayout进行平均分配 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- 第一行：取餐号、用餐方式、编号 -->
        <LinearLayout
            android:id="@+id/llPickUpInfo"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvPickUpNo"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:ellipsize="middle"
                android:gravity="start|center_vertical"
                android:maxLines="1"
                android:textColor="@color/black"
                android:textSize="24px"
                android:textStyle="bold"
                tools:text="A2800/"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvDingstyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="middle"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:textColor="@color/black"
                android:textSize="24px"
                android:textStyle="bold"
                tools:text="ញុាំនៅហាង" />

            <TextView
                android:id="@+id/tvNumber"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5px"
                android:ellipsize="middle"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:textColor="@color/black"
                android:textSize="16px"
                android:textStyle="bold"
                android:visibility="visible"
                tools:text="1/1"
                tools:visibility="visible" />
        </LinearLayout>

        <!-- 第二行：商品名称 -->
        <LinearLayout
            android:id="@+id/llGoodName"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tvGoodName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="start|center_vertical"
                android:maxLines="1"
                android:textColor="@color/black"
                android:textSize="24px"
                android:textStyle="bold"
                tools:text="សម្រាកបណ្តោះអាសន្សម្រាកបណ្តោះអាសន្នសម្រាកបណ្តោះអាសន្នន" />
        </LinearLayout>

        <!-- 第三行：标签 -->
        <LinearLayout
            android:id="@+id/llTag"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tvTag"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="start|center_vertical"
                android:maxLines="2"
                android:textColor="@color/black"
                android:textSize="16px"
                android:textStyle="bold"
                tools:text="សម្រាកបណ្តោះអាសន្សម្រាកបណ្តោះអាសន្នសម្រាកបណ្តោះអាសន្នន" />
        </LinearLayout>

        <!-- 第四行：反馈 -->
        <LinearLayout
            android:id="@+id/llFeed"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tvFeed"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="start|center_vertical"
                android:maxLines="2"
                android:textColor="@color/black"
                android:textSize="16px"
                android:textStyle="bold"
                tools:text="សម្រាកបណ្តោះអាសន្សម្រាកបណ្តោះអាសន្នសម្រាកបណ្តោះអាសន្នន" />
        </LinearLayout>

    </LinearLayout>

    <!-- 第五行：标语 -->
    <LinearLayout
        android:id="@+id/llSlogan"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:layout_marginBottom="5px">

        <TextView
            android:id="@+id/tvSlogan"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="start|center_vertical"
            android:maxLines="1"
            android:paddingEnd="10px"
            android:textColor="@color/black"
            android:textSize="16px"
            android:textStyle="bold"
            android:visibility="visible"
            tools:text="សម្រាកសម្រាកបណ្តោះអាសន្នសម្រាកបណ្តោះអាសន្សម្រាកបណ្តោះអាសន្នសម្រាកបណ្តោះអាសន្ននបណ្តោះអាសន្នសម្រាកបណ្តោះអាសន្សម្រាកបណ្តោះអាសន្នសម្រាកបណ្តោះអាសន្នន"
            tools:visibility="visible" />
    </LinearLayout>

    <!-- 最后一行：支付时间和价格 -->
    <LinearLayout
        android:id="@+id/llBottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvPayTime"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="start"
            android:lines="1"
            android:textColor="@color/black"
            android:textSize="16px"
            android:textStyle="bold"
            tools:text="2024/10/10 13:20" />

        <TextView
            android:id="@+id/tvPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:textSize="21px"
            android:textStyle="bold"
            tools:text="$30.00" />
    </LinearLayout>

</LinearLayout>